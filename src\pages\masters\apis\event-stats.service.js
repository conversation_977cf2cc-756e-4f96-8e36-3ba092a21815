import axiosServices from 'utils/axios';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

/**
 * Get comprehensive event statistics for NGO or all events
 * @param {number|null} ngoId - The NGO ID (optional for admin view)
 * @returns {Promise} API response with event statistics
 */
export const getNgoEventStats = async (ngoId = null) => {
  try {
    const url = ngoId ? `${API_BASE_URL}/campaigns/stats/ngoEventStats?ngoId=${ngoId}` : `${API_BASE_URL}/campaigns/stats/ngoEventStats`;

    const response = await axiosServices.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching NGO event stats:', error);
    throw error;
  }
};

/**
 * Get detailed statistics for a single event
 * @param {number} eventId - The event ID
 * @returns {Promise} API response with single event statistics
 */
export const getSingleEventStats = async (eventId) => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/campaigns/stats/singleEventStats?eventId=${eventId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching single event stats:', error);
    throw error;
  }
};
