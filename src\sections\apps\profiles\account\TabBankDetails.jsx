import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Alert, Box, Button, Card, CardContent, CircularProgress, Grid, Stack, TextField, Typography } from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  updateBankDetails,
  getAllBankDetails,
  addBankDetails,
  createRazorpayAccount,
  getRazorpayActivationStatus,
  deleteBankDetails,
  getBankDetailsByIFSC
} from './tabs.service';
import useAuth from 'hooks/useAuth';
import UnsavedChangesDialog from './UnsavedChangesDialog';
import { getLoggedInNGOInfo } from '../profile.service';
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import BankDetailsConfirmDialog from './BankDetailsConfirmDialog';

export default function TabBankDetails() {
  const { newNgoId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [bankDetails, setBankDetails] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Razorpay activation status
  const [activationStatus, setActivationStatus] = useState(null);
  const [isActivationLoading, setIsActivationLoading] = useState(false);
  const [ngoInfo, setNgoInfo] = useState(null);

  //trace changes
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigationPath, setPendingNavigationPath] = useState(null);

  // Disable fields if bank details exist
  const isDisabled = !!bankDetails;

  // New state for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingBankDetails, setPendingBankDetails] = useState(null);
  const [confirmDialogLoading, setConfirmDialogLoading] = useState(false);

  // New state for IFSC fetch
  const [ifscFetching, setIfscFetching] = useState(false);
  const [ifscError, setIfscError] = useState('');

  useEffect(() => {
    fetchBankDetails();

    fetchNGODetails();
  }, []);

  const fetchNGODetails = async () => {
    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId); // replace with your actual endpoint
        setNgoInfo(response);
        return;
      }
      const response = await getLoggedInNGOInfo(user?.ngo_id); // replace with your actual endpoint
      setNgoInfo(response);
    } catch (error) {
      console.error('Error fetching ngo details:', error);
      toast.error('Failed to fetch ngo details');
    }
  };

  const fetchBankDetails = async () => {
    setIsLoading(true);
    try {
      const response = await getAllBankDetails(user?.ngo_id || newNgoId);
      if (response && response.length > 0) {
        !!response[0]?.bank_name && fetchActivationStatus();
        setBankDetails(response[0]); // Assume only one bank detail exists
      }
    } catch (error) {
      console.error('Error fetching bank details:', error);
      toast.error('Failed to fetch bank details');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchActivationStatus = async () => {
    setIsActivationLoading(true);
    try {
      const ngo_id = user?.ngo_id || newNgoId;
      if (!ngo_id) return;
      const res = await getRazorpayActivationStatus(ngo_id);
      setActivationStatus(res);
    } catch (error) {
      setActivationStatus(null);
      // toast.error('Failed to fetch Razorpay activation status');
    } finally {
      setIsActivationLoading(false);
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      beneficiary_name: ngoInfo?.name || '',
      bank_name: bankDetails?.bank_name || '',
      branch_name: bankDetails?.branch_name || '',
      branch_address: bankDetails?.branch_address || '',
      account_number: bankDetails?.account_number || '',
      confirm_account_number: bankDetails?.account_number || '', // Set default
      ifsc_code: bankDetails?.ifsc_code || ''
    },
    validationSchema: Yup.object({
      beneficiary_name: Yup.string().required('Beneficiary Name is required'),
      bank_name: Yup.string().required('Bank Name is required'),
      branch_name: Yup.string().required('Branch Name is required'),
      branch_address: Yup.string().required('Branch Address is required'),
      account_number: Yup.string()
        .matches(/^\d{9,18}$/, 'Account Number must be 9-18 digits')
        .required('Account Number is required'),
      confirm_account_number: Yup.string()
        .oneOf([Yup.ref('account_number'), null], 'Account Numbers must match')
        .required('Confirm Account Number is required'),
      ifsc_code: Yup.string()
        .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Enter a valid IFSC Code (Example: HDFC0123456)')
        .required('IFSC Code is required')
    }),
    onSubmit: async (values) => {
      if (!bankDetails) {
        // Show confirmation dialog before submitting new bank details
        setPendingBankDetails(values);
        setShowConfirmDialog(true);
        return;
      }
      setIsSubmitting(true);
      try {
        const payload = {
          ...values,
          ngo_id: user?.ngo_id ? user?.ngo_id : newNgoId,
          status: 'Active'
        };
        await updateBankDetails(bankDetails.id, payload);
        toast.success('Bank details updated successfully');
        setTimeout(() => {
          fetchBankDetails();
          setValuesChanged(false);
          setIsSubmitting(false);
        }, 2000);
      } catch (error) {
        console.error('Error updating bank details:', error);
        toast.error('Failed to update bank details');
        setIsSubmitting(false);
      }
    }
  });

  const handleNavigation = (path) => {
    if (isValuesChanged) {
      setPendingNavigationPath(path);
      setShowUnsavedDialog(true);
    } else {
      navigate(path);
    }
  };

  useEffect(() => {
    if (JSON.stringify(formik.values) !== JSON.stringify(formik.initialValues)) {
      setValuesChanged(true);
    }
  }, [formik.values, formik.initialValues]);

  // Handler for confirming submission in dialog
  const handleConfirmSubmit = async () => {
    setConfirmDialogLoading(true);
    setIsSubmitting(true);
    let createdBank = null;
    try {
      const payload = {
        ...pendingBankDetails,
        ngo_id: user?.ngo_id ? user?.ngo_id : newNgoId,
        status: 'Active'
      };
      // Call Razorpay account creation API first
      createdBank = await addBankDetails(payload);
      const razorpayRes = await createRazorpayAccount({
        ngo_id: payload.ngo_id,
        account_number: payload.account_number,
        ifsc_code: payload.ifsc_code,
        beneficiary_name: payload.beneficiary_name
      });
      if (!razorpayRes.success || !razorpayRes.data || !razorpayRes.data.product_enabled) {
        throw new Error('Failed to activate Razorpay account. Please check details or try again.');
      }
      toast.success('Bank details Added successfully');
      setTimeout(() => {
        fetchBankDetails();
        setValuesChanged(false);
        setIsSubmitting(false);
        setConfirmDialogLoading(false);
        setShowConfirmDialog(false);
      }, 2000);
    } catch (error) {
      // If we created a bank record, delete it
      if (createdBank?.data?.id) {
        try {
          await deleteBankDetails(createdBank.data.id);
        } catch (deleteError) {
          console.error('Failed to delete bank details after error:', deleteError);
        }
      }
      console.error('Error updating bank details:', error);
      const description = error?.details?.error?.description || error?.response?.data?.details?.error?.description || error?.message;
      if (description) {
        toast.error(description);
      } else {
        toast.error('Failed to update bank details');
      }
      setIsSubmitting(false);
      setConfirmDialogLoading(false);
    }
  };

  // IFSC auto-fill effect
  useEffect(() => {
    const fetchIFSCDetails = async () => {
      if (
        formik.values.ifsc_code &&
        /^[A-Z]{4}0[A-Z0-9]{6}$/.test(formik.values.ifsc_code) &&
        !bankDetails // Only for new entry
      ) {
        setIfscFetching(true);
        setIfscError('');
        try {
          const response = await getBankDetailsByIFSC(formik.values.ifsc_code);
          if (response?.status) {
            const data = response?.data;
            formik.setFieldValue('bank_name', data.BANK || '', false);
            formik.setFieldValue('branch_name', data.BRANCH || '', false);
            formik.setFieldValue('branch_address', data.ADDRESS || '', false);
            formik.setFieldValue('city', data.CITY || '', false);
            formik.setFieldValue('state', data.STATE || '', false);
          }
        } catch (err) {
          setIfscError('Invalid IFSC code or details not found.');
        } finally {
          setIfscFetching(false);
        }
      }
    };
    fetchIFSCDetails();
    // eslint-disable-next-line
  }, [formik.values.ifsc_code]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Bank Details...</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Razorpay Activation Status Section */}
        {activationStatus && (
          <Box
            mb={2}
            p={2}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderRadius: 2,
              backgroundColor: activationStatus.activation_status === 'activated' ? '#e6f4ea' : '#fff7e6',
              border: `1px solid ${activationStatus.activation_status === 'activated' ? '#b7eb8f' : '#ffe58f'}`
            }}
          >
            {/* Left: Status icon + message */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {activationStatus.activation_status === 'activated' ? (
                <CheckCircleOutlined style={{ fontSize: 20, color: '#52c41a', marginRight: 8 }} />
              ) : (
                <ExclamationCircleOutlined style={{ fontSize: 20, color: '#faad14', marginRight: 8 }} />
              )}

              <Typography variant="body1">
                Account Verification Status: <b>{activationStatus.activation_status}</b>
              </Typography>
            </Box>

            {/* Right: Refresh button */}
            {activationStatus.activation_status !== 'activated' && (
              <Button size="small" variant="contained" onClick={fetchActivationStatus} disabled={isActivationLoading}>
                {isActivationLoading ? <CircularProgress size={18} color="inherit" /> : 'Refresh Status'}
              </Button>
            )}
          </Box>
        )}

        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Beneficiary Name"
                name="beneficiary_name"
                disabled
                required
                {...formik.getFieldProps('beneficiary_name')}
                error={formik.touched.beneficiary_name && Boolean(formik.errors.beneficiary_name)}
                helperText={formik.touched.beneficiary_name && formik.errors.beneficiary_name}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Account Number"
                name="account_number"
                type="password"
                autoComplete="off"
                required
                {...formik.getFieldProps('account_number')}
                error={formik.touched.account_number && Boolean(formik.errors.account_number)}
                helperText={formik.touched.account_number && formik.errors.account_number}
                disabled={isDisabled}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Confirm Account Number"
                name="confirm_account_number"
                required
                {...formik.getFieldProps('confirm_account_number')}
                error={formik.touched.confirm_account_number && Boolean(formik.errors.confirm_account_number)}
                helperText={formik.touched.confirm_account_number && formik.errors.confirm_account_number}
                autoComplete="off"
                disabled={isDisabled}
              />
            </Grid>
            {/* IFSC Code field after Beneficiary Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="IFSC Code"
                name="ifsc_code"
                required
                {...formik.getFieldProps('ifsc_code')}
                error={formik.touched.ifsc_code && (Boolean(formik.errors.ifsc_code) || !!ifscError)}
                helperText={formik.touched.ifsc_code && (formik.errors.ifsc_code || ifscError)}
                disabled={isDisabled}
                InputProps={{
                  endAdornment: ifscFetching ? <CircularProgress size={18} color="primary" /> : null
                }}
              />
            </Grid>
            {/* Bank Name, Branch Name, Branch Address, City, State fields auto-filled and disabled if IFSC is valid */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bank Name"
                name="bank_name"
                required
                {...formik.getFieldProps('bank_name')}
                error={formik.touched.bank_name && Boolean(formik.errors.bank_name)}
                helperText={formik.touched.bank_name && formik.errors.bank_name}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Branch Name"
                name="branch_name"
                required
                {...formik.getFieldProps('branch_name')}
                error={formik.touched.branch_name && Boolean(formik.errors.branch_name)}
                helperText={formik.touched.branch_name && formik.errors.branch_name}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Branch Address"
                name="branch_address"
                required
                {...formik.getFieldProps('branch_address')}
                error={formik.touched.branch_address && Boolean(formik.errors.branch_address)}
                helperText={formik.touched.branch_address && formik.errors.branch_address}
                autoComplete="off"
                disabled
                multiline
                minRows={3}
              />
            </Grid>

            {/* Update Button */}
            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={
                  bankDetails?.ngoInfo?.ngo_status == 'Verified' ||
                  bankDetails?.ngoInfo?.ngo_status == 'In Review' ||
                  isDisabled ||
                  isSubmitting
                }
              >
                {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </form>
        <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 2 }}>
          <Stack spacing={2} alignItems="flex-end">
            <Button
              variant="contained"
              color="primary"
              onClick={() => handleNavigation(newNgoId ? `/masters/ngos/edit/socials/${newNgoId}` : '/apps/profiles/account/socials')}
            >
              Back
            </Button>

            <Alert severity="error">
              Note: If you click 'Next' or 'Back', any unsaved changes will be discarded. Be sure to click 'Save' first.
            </Alert>
          </Stack>
        </Grid>
      </CardContent>
      <UnsavedChangesDialog
        showUnsavedDialog={showUnsavedDialog}
        setShowUnsavedDialog={setShowUnsavedDialog}
        pendingNavigationPath={pendingNavigationPath}
      />
      <BankDetailsConfirmDialog
        open={showConfirmDialog}
        onClose={() => !confirmDialogLoading && setShowConfirmDialog(false)}
        onSubmit={handleConfirmSubmit}
        details={pendingBankDetails || {}}
        loading={confirmDialogLoading}
      />
      <ToastContainer autoClose={6000} />
    </Card>
  );
}
