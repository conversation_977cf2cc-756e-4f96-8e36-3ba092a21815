import React, { useEffect, useState } from 'react';
import axios from 'axios';
import CustomReactTable from 'components/CustomerDataGrid';
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  FormControlLabel,
  FormGroup,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { API_BASE_URL } from 'api/categories.service';
import { getUserPermissions } from 'utils/permissionUtils';
import TableActions from 'components/TableActions';
import { COMMON_STATUS_LIST } from 'utils/statusconstans';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';

import useAuth from 'hooks/useAuth';
import { addRoleService, deleteRoleService, fetchRolesService, updateRoleService } from './apis/role.service';
import dayjs from 'dayjs';

export default function RolesTable() {
  const { user } = useAuth(); //no getting roles object so below
  const roleInfo = JSON.parse(localStorage.getItem('user'))?.roleInfo || {};
  const [roles, setRoles] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentRole, setCurrentRole] = useState({ name: '', type: '', status: 'Active', permissions: {} });
  const [openPermissionsDialog, setOpenPermissionsDialog] = useState(false);
  const [viewRolePermissions, setViewRolePermissions] = useState({});

  //retrieve from local storage
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.Roles || false;
  const canEdit = permissions?.Edit?.Roles || false;
  const canDelete = permissions?.Delete?.Roles || false;
  const canEditRole = roleInfo.name === 'DR_Management';

  useEffect(() => {
    fetchRoles();
  }, []);

  const fetchRoles = async () => {
    try {
      const roleInfo = JSON.parse(localStorage.getItem('user'))?.roleInfo || {};
      const response = await fetchRolesService();
      const filteredRoles = response.filter((role) => {
        if (roleInfo.name === 'NGO_Management' || roleInfo.name === 'NGO_Staff') {
          return ['NGO_Management', 'NGO_Staff'].includes(role.name);
        }
        return true;
      });
      setRoles(filteredRoles);
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      toast.error('Failed to fetch roles');
    }
  };

  const handleAddOrEdit = async () => {
    try {
      if (isEditing) {
        await updateRoleService(currentRole.id, currentRole);

        toast.success('Role updated successfully!');
      } else {
        await addRoleService(currentRole);
        toast.success('Role added successfully!');
      }
      setOpenDialog(false);
      fetchRoles();
    } catch (error) {
      console.error('Failed to save role:', error);
      toast.error('Failed to save role');
    }
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to Delete Roles.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this role?');
    if (confirmDelete) {
      try {
        await deleteRoleService(id);
        toast.success('Role deleted successfully!');
        fetchRoles();
      } catch (error) {
        console.error('Failed to delete role:', error);
        toast.error('Failed to delete role');
      }
    }
  };
  const handleShowPermissions = (role) => {
    const parsedPermissions = JSON.parse(role.permissions);
    setViewRolePermissions(parsedPermissions);
    setOpenPermissionsDialog(true);
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Roles.');
      return;
    }
    setCurrentRole({ name: '', slug: '', type: '', status: 'Active', permissions: {} });
    setIsEditing(false);
    setOpenDialog(true);
  };

  const openEditDialog = (role) => {
    if (!canEdit) {
      toast.error('You do not have permission to edit Roles.');
      return;
    }
    const parsedPermissions = JSON.parse(role.permissions);
    setCurrentRole({ ...role, permissions: parsedPermissions });
    setIsEditing(true);
    setOpenDialog(true);
  };
  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.name}
          </Button>
        );
      }
    },
    { accessorKey: 'type', header: 'Type', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => (
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          {canEditRole && (
            <EditOutlined
              style={{ fontSize: '18px', color: 'orange', cursor: 'pointer' }}
              onClick={() => openEditDialog(cell.row.original)}
              title="Edit Role"
            />
          )}
          <EyeOutlined
            style={{ fontSize: '18px', color: 'blue', cursor: 'pointer' }}
            onClick={() => handleShowPermissions(cell.row.original)}
            title="Show Permissions"
          />
        </div>
      )
      // meta: {
      //   className: 'cell-center'
      // },
      // disableSortBy: true,
      // cell: (cell, { row }) => {
      //   // const collapseIcon =
      //   //   row.getCanExpand() && row.getIsExpanded() ? <PlusOutlined style={{ transform: 'rotate(45deg)' }} /> : <EyeOutlined />;
      // return <TableActions handleEditClick={openEditDialog} cell={cell} handleDeleteClick={handleDelete} />;
      // }
    }
  ];
  const handlePermissionChange = (category, permission) => {
    setCurrentRole((prevRole) => ({
      ...prevRole,

      permissions: {
        ...prevRole.permissions,

        [category]: {
          ...prevRole.permissions[category],

          [permission]: !prevRole.permissions[category]?.[permission]
        }
      }
    }));
  };

  const permissionsStructure = {
    ShowSidebar: [
      'Campaigns',
      'Categories',
      'Collections',
      'Document',
      'EmailTemplates',
      'Faqs',
      'Items',
      'NGOS',
      'NgoTypes',
      'NewsLetters',
      'Products',
      'Questions',
      'Roles',
      'SearchNgo',
      'SearchPan',
      'Skills',
      'Slides',
      'StaffMembers',
      'SubCategories',
      'themes',
      'Transactions',
      'Users',
      'Events',
      'Orders',
      'Buckets'
    ],

    Add: [
      'Campaigns',
      'Categories',
      'Collections',
      'Document',
      'EmailTemplates',
      'Faqs',
      'Items',
      'NGOS',
      'NgoTypes',
      'NewsLetters',
      'Products',
      'Questions',
      'Roles',
      'SearchNgo',
      'SearchPan',
      'Skills',
      'Slides',
      'StaffMembers',
      'SubCategories',
      'themes',
      'Transactions',
      'Users',
      'Events',
      'Orders',
      'Buckets'
    ],

    Edit: [
      'Campaigns',
      'Categories',
      'Collections',
      'Document',
      'EmailTemplates',
      'Faqs',
      'Items',
      'NGOS',
      'NgoTypes',
      'NewsLetters',
      'Products',
      'Questions',
      'Roles',
      'SearchNgo',
      'SearchPan',
      'Skills',
      'Slides',
      'StaffMembers',
      'SubCategories',
      'themes',
      'Transactions',
      'Users',
      'Events',
      'Orders',
      'Buckets'
    ],

    Delete: [
      'Campaigns',
      'Categories',
      'Collections',
      'Document',
      'EmailTemplates',
      'Faqs',
      'Items',
      'NGOS',
      'NgoTypes',
      'NewsLetters',
      'Products',
      'Questions',
      'Roles',
      'SearchNgo',
      'SearchPan',
      'Skills',
      'Slides',
      'StaffMembers',
      'SubCategories',
      'themes',
      'Transactions',
      'Users',
      'Events',
      'Orders',
      'Buckets'
    ],

    View: [
      'Campaigns',
      'Categories',
      'Collections',
      'Document',
      'EmailTemplates',
      'Faqs',
      'Items',
      'NGOS',
      'NgoTypes',
      'NewsLetters',
      'Products',
      'Questions',
      'Roles',
      'SearchNgo',
      'SearchPan',
      'Skills',
      'Slides',
      'StaffMembers',
      'SubCategories',
      'themes',
      'Transactions',
      'Users',
      'Events',
      'Orders',
      'Buckets'
    ]
  };

  const statusList = COMMON_STATUS_LIST;
  return (
    <div>
      <CustomerTable data={roles} columns={columns} modalToggler={openAddDialog} category={'Role'} statusList={statusList} />
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>{isEditing ? 'Edit Role' : 'Add Role'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={currentRole?.name || ''}
            onChange={(e) => setCurrentRole({ ...currentRole, name: e.target.value })}
          />

          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Type</InputLabel>
            <Select value={currentRole?.type || ''} onChange={(e) => setCurrentRole({ ...currentRole, type: e.target.value })} label="Type">
              <MenuItem value="Admin">Admin</MenuItem>
              <MenuItem value="NGO">NGO</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth margin="dense" variant="outlined">
            <InputLabel>Status</InputLabel>
            <Select
              value={currentRole?.status || ''}
              onChange={(e) => setCurrentRole({ ...currentRole, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="Active">Active</MenuItem>
              <MenuItem value="Inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
          {Object.keys(permissionsStructure).map((category) => {
            return (
              <FormControl component="fieldset" key={category} margin="normal">
                <h3>{category}</h3>

                <FormGroup>
                  {permissionsStructure[category].map((permission) => {
                    return (
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={currentRole.permissions?.[category]?.[permission] || false}
                            onChange={() => handlePermissionChange(category, permission)}
                            name={`${category}_${permission}`}
                          />
                        }
                        label={permission}
                        key={permission}
                      />
                    );
                  })}
                </FormGroup>
              </FormControl>
            );
          })}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleAddOrEdit} color="primary">
            {isEditing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openPermissionsDialog}
        onClose={() => setOpenPermissionsDialog(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>View Permissions</DialogTitle>
        <DialogContent>
          {Object.keys(viewRolePermissions).map((category) => {
            const categoryPermissions = viewRolePermissions[category];
            const selectedPermissions = Object.keys(categoryPermissions).filter(
              (permission) => categoryPermissions[permission] // if role present show
            );

            // no permission dont show
            if (selectedPermissions.length === 0) return null;

            return (
              <FormControl component="fieldset" key={category} margin="normal">
                <h3>{category}</h3>
                <FormGroup>
                  {selectedPermissions.map((permission) => (
                    <FormControlLabel control={<Checkbox checked={true} />} label={permission} key={permission} />
                  ))}
                </FormGroup>
              </FormControl>
            );
          })}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenPermissionsDialog(false)} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
