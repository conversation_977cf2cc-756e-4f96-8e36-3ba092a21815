import { useEffect } from 'react';
// material-ui
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Card, CardContent, Chip, Avatar, LinearProgress } from '@mui/material';
import {
  RiseOutlined,
  TeamOutlined,
  BulbOutlined,
  DollarOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  BankOutlined,
  UserOutlined,
  HeartOutlined,
  BarChartOutlined
} from '@ant-design/icons';

// project import
import MainCard from 'components/MainCard';

import { API_BASE_URL } from 'api/campaigns.service';
import ClusteredMapComponent from 'pages/maps/ClusteredMapComponent';
import { Box, CircularProgress } from '@mui/material';
import { Fragment, useState } from 'react';
import { Link } from 'react-router-dom';
import useAuth from 'hooks/useAuth';
import AlertMessage from './Alertmessage';
import AnalyticEcommerce from 'components/cards/statistics/AnalyticEcommerce';
import { getFullCampaignStats, getFullNGOStats } from 'pages/masters/apis/notification.service';
import { getProfileQueries, getDashboardStats } from 'sections/apps/profiles/account/tabs.service';

// ==============================|| DASHBOARD - ANALYTICS ||============================== //

export default function DashboardAnalytics() {
  const [isLoadingNgo, setIsLoadingNgo] = useState(false);
  const [isLoadingCampaign, setIsLoadingCampaign] = useState(false);
  const { user } = useAuth();

  const [ngoStatsAll, setNgoStatsAll] = useState({
    totalNgos: 0,
    inReviewNgos: 0,
    pendingNgos: 0,
    verifiedNgos: 0,
    inactiveNgos: 0
  });
  const [campaignStatsAll, setCampaignStatsAll] = useState({
    totalCampaigns: 0,
    live: 0,
    inReview: 0,
    approved: 0,
    completed: 0
  });

  const [profileQueries, setProfileQueries] = useState([]);

  const [dashboardStats, setDashboardStats] = useState({
    dailyActiveUsers: { count: 0, description: '' },
    donationAnalytics: {
      totalDonationAmount: 0,
      uniqueUserCount: 0,
      averageDonationPerUser: 0,
      description: ''
    },
    volunteerCount: { count: 0, description: '' },
    campaignCTR: { campaignViews: 0, ctr: 0, description: '' },
    todaysCampaignCount: { count: 0, description: '' },
    todaysTotalDonations: { totalAmount: 0, totalOrders: 0, description: '' },
    ngosOnboarded: { count: 0, description: '' },
    todaysCampaigns: { campaigns: [], totalCount: 0, description: '' },
    ngosAssignedLast5Days: { ngos: [], totalCount: 0, description: '' }
  });
  const [isDashboardLoading, setIsDashboardLoading] = useState(false);

  useEffect(() => {
    const fetchFullNgoStats = async () => {
      setIsLoadingNgo(true);
      try {
        const response = await getFullNGOStats();
        setNgoStatsAll(response.ngoStats);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      } finally {
        setIsLoadingNgo(false);
      }
    };

    const fetchFullCampaignStats = async () => {
      setIsLoadingCampaign(true);
      try {
        const response = await getFullCampaignStats();
        setCampaignStatsAll(response.campaginStats);
      } catch (error) {
        console.error('Error fetching campaign stats:', error);
      } finally {
        setIsLoadingCampaign(false);
      }
    };

    const fetchDashboardStats = async () => {
      setIsDashboardLoading(true);
      try {
        const response = await getDashboardStats();
        if (response.status && response.data) {
          setDashboardStats(response.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setIsDashboardLoading(false);
      }
    };

    const fetchProfileQueries = async () => {
      try {
        const response = await getProfileQueries(null, user?.roleInfo?.name === 'DR_Management' ? null : user?.id, 'profile-queries');
        setProfileQueries(response);
      } catch (error) {
        console.error('Error fetching NGO stats:', error);
      }
    };

    fetchProfileQueries();
    fetchDashboardStats();
    if (user.roleInfo.name === 'DR_Management') {
      fetchFullCampaignStats();
      fetchFullNgoStats();
    }
  }, [user]);

  return (
    <Fragment>
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        {/* row 1 */}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inReviewNgos === 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inReviewNgos} NGO is currently under review.`} status="In Review" type="ngo" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inReviewNgos > 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inReviewNgos} NGOs are currently under review.`} status="In Review" type="ngo" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inactiveNgos === 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inactiveNgos} NGO is currently inactive`} status="Inactive" type="portaluser" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && ngoStatsAll.inactiveNgos > 1 && (
          <Grid item xs={6}>
            <AlertMessage message={`${ngoStatsAll.inactiveNgos} NGOs are currently inactive.`} status="Inactive" type="portaluser" />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && campaignStatsAll.inReview === 1 && (
          <Grid item xs={6}>
            <AlertMessage
              message={`${campaignStatsAll.inReview} campaigns is currently under review.`}
              status="In Review"
              type="campaign"
            />
          </Grid>
        )}
        {user?.roleInfo?.name === 'DR_Management' && campaignStatsAll.inReview > 1 && (
          <Grid item xs={6}>
            <AlertMessage
              message={`${campaignStatsAll.inReview} campaigns are currently under review.`}
              status="In Review"
              type="campaign"
            />
          </Grid>
        )}

        {profileQueries?.length > 0 && (
          <Grid item xs={6} style={{}}>
            <AlertMessage
              message={`${profileQueries?.length} NGO(s) have requested profile edits. Please review their requests.`}
              status="Pending"
              type="profile-query"
            />
          </Grid>
        )}
      </Grid>
      <hr />
      {/* <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total NGO's" count="73,952" percentage={70.5}>
          <UsersCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total Campaigns" count="10" percentage={27.4} isLoss color="warning">
          <OrdersCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Active Members" count="35,078" percentage={27.4} isLoss color="warning">
          <SalesCardChart />
        </AnalyticsDataCard>
      </Grid>
      <Grid item xs={12} sm={6} md={4} lg={3}>
        <AnalyticsDataCard title="Total Members" count="112083" percentage={70.5}>
          <MarketingCardChart />
        </AnalyticsDataCard>
      </Grid> */}

      {user?.roleInfo?.name === 'DR_Management' && (
        <Grid container rowSpacing={4.5} columnSpacing={2.75} sx={{ mt: 2 }}>
          <Grid item xs={12} sx={{ mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <BarChartOutlined style={{ color: '#1976d2', fontSize: 28 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Dashboard Analytics
              </Typography>
            </Box>
          </Grid>
          {isDashboardLoading ? (
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress />
              <Typography sx={{ margin: 2 }}>Loading Dashboard Analytics...</Typography>
            </Grid>
          ) : (
            <>
              {/* Top Analytics Cards */}
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    // background: '#fef8f3',
                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    // boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      // boxShadow: '0 8px 25px rgba(255, 174, 95, 0.2)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 48,
                          height: 48,
                          boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                        }}
                      >
                        <UserOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                      </Avatar>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        {dashboardStats.dailyActiveUsers.count}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                      Daily Active Users
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Active users today
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    // background: '#fef8f3',

                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    // boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      // boxShadow: '0 8px 25px rgba(255, 174, 95, 0.2)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 48,
                          height: 48,
                          boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                        }}
                      >
                        <DollarOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                      </Avatar>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        ₹{dashboardStats.donationAnalytics.averageDonationPerUser?.toLocaleString() || 0}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                      Avg Donation Per User
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Average contribution
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    // background: '#fef8f3',

                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    // boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      // boxShadow: '0 8px 25px rgba(255, 174, 95, 0.2)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 48,
                          height: 48,
                          boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                        }}
                      >
                        <HeartOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                      </Avatar>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        {dashboardStats.volunteerCount.count}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                      Volunteer Sign Ups
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      New volunteers
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    // background: '#fef8f3',

                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    // boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      // boxShadow: '0 8px 25px rgba(255, 174, 95, 0.2)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 48,
                          height: 48,
                          boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                        }}
                      >
                        <RiseOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                      </Avatar>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        {dashboardStats.campaignCTR.ctr?.toFixed(2) || 0}%
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                      Campaign CTR
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Click-through rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Daily Targets Section */}
              <Grid item xs={12} sx={{ mt: 4, mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <RiseOutlined style={{ color: '#1976d2', fontSize: 28 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                    Daily Targets
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    background: '#fef8f3',

                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      // boxShadow: '0 10px 30px rgba(255, 174, 95, 0.25)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2.5 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 56,
                          height: 56,
                          boxShadow: '0 3px 10px rgba(255, 174, 95, 0.25)'
                        }}
                      >
                        <TeamOutlined style={{ color: '#ffae5f', fontSize: 26 }} />
                      </Avatar>
                      <Typography variant="h3" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        {dashboardStats.ngosOnboarded.count}
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                      NGO Onboarding
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                      New NGOs onboarded today
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    background: '#fef8f3',
                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      // boxShadow: '0 10px 30px rgba(255, 174, 95, 0.25)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2.5 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 56,
                          height: 56,
                          boxShadow: '0 3px 10px rgba(255, 174, 95, 0.25)'
                        }}
                      >
                        <BulbOutlined style={{ color: '#ffae5f', fontSize: 26 }} />
                      </Avatar>
                      <Typography variant="h3" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        {dashboardStats.todaysCampaignCount.count}
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                      Content Posting
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                      Campaigns created today
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card
                  sx={{
                    height: '100%',
                    // background: 'linear-gradient(135deg, #ffffff 0%, #fef8f3 100%)',
                    background: '#fef8f3',
                    border: '1px solid rgba(255, 174, 95, 0.2)',
                    borderRadius: 3,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.08)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      // boxShadow: '0 10px 30px rgba(255, 174, 95, 0.25)',
                      borderColor: '#ffae5f',
                      background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                    }
                  }}
                >
                  <CardContent sx={{ p: 3.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2.5 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'rgba(255, 174, 95, 0.15)',
                          width: 56,
                          height: 56,
                          boxShadow: '0 3px 10px rgba(255, 174, 95, 0.25)'
                        }}
                      >
                        <DollarOutlined style={{ color: '#ffae5f', fontSize: 26 }} />
                      </Avatar>
                      <Typography variant="h3" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                        ₹{dashboardStats.todaysTotalDonations.totalAmount?.toLocaleString() || 0}
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                      Today's Donations
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                      Total amount raised today
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* NGO Onboarding and Documents Section */}
              <Grid item xs={12} sx={{ mt: 4, mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <BankOutlined style={{ color: '#1976d2', fontSize: 28 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                    NGO Onboarding and Documents
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    border: '1px solid rgba(0,0,0,0.05)'
                  }}
                >
                  <CardContent sx={{ p: 0 }}>
                    {dashboardStats.ngosAssignedLast5Days.ngos.length === 0 ? (
                      <Box sx={{ p: 6, textAlign: 'center' }}>
                        <FileTextOutlined style={{ fontSize: 64, color: '#bdbdbd', marginBottom: 16 }} />
                        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                          No Recent NGO Assignments
                        </Typography>
                        <Typography variant="body2" color="text.disabled">
                          No NGOs have been assigned in the last 5 days
                        </Typography>
                      </Box>
                    ) : (
                      <Box sx={{ overflowX: 'auto' }}>
                        <Box
                          sx={{
                            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                            p: 2,
                            borderBottom: '1px solid rgba(0,0,0,0.08)'
                          }}
                        >
                          <Grid container spacing={2} sx={{ fontWeight: 600, color: 'text.primary' }}>
                            <Grid item xs={12} sm={3}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                NGO Details
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={2}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Status
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={2}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Timeline
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={3}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Document Progress
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={2}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Completion
                              </Typography>
                            </Grid>
                          </Grid>
                        </Box>

                        {dashboardStats.ngosAssignedLast5Days.ngos.map((ngo, index) => (
                          <Box
                            key={ngo.id}
                            sx={{
                              p: 2,
                              borderBottom:
                                index < dashboardStats.ngosAssignedLast5Days.ngos.length - 1 ? '1px solid rgba(0,0,0,0.05)' : 'none',
                              '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.02)',
                                transition: 'background-color 0.2s ease'
                              }
                            }}
                          >
                            <Grid container spacing={2} alignItems="center">
                              <Grid item xs={12} sm={3}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                                  <Avatar
                                    sx={{
                                      bgcolor: 'primary.main',
                                      width: 40,
                                      height: 40,
                                      fontSize: '0.875rem'
                                    }}
                                  >
                                    {ngo.name?.charAt(0)?.toUpperCase() || 'N'}
                                  </Avatar>
                                  <Box>
                                    <Typography variant="body2" fontWeight={600} sx={{ mb: 0.5 }}>
                                      {ngo.name}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {ngo.current_address || 'Address not provided'}
                                    </Typography>
                                  </Box>
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={2}>
                                <Chip
                                  label={ngo.ngo_status}
                                  size="small"
                                  sx={{
                                    backgroundColor:
                                      ngo.ngo_status === 'Verified' ? '#e8f5e8' : ngo.ngo_status === 'New' ? '#fff3e0' : '#f3e5f5',
                                    color: ngo.ngo_status === 'Verified' ? '#2e7d32' : ngo.ngo_status === 'New' ? '#f57c00' : '#7b1fa2',
                                    fontWeight: 600,
                                    '& .MuiChip-label': { px: 1.5 }
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12} sm={2}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <ClockCircleOutlined style={{ fontSize: 16, color: '#757575' }} />
                                  <Typography variant="body2" fontWeight={500}>
                                    {ngo.daysPassed} days
                                  </Typography>
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={3}>
                                <Box sx={{ width: '100%' }}>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                                    <Typography variant="caption" color="text.secondary">
                                      Documents
                                    </Typography>
                                    <Typography variant="caption" fontWeight={600}>
                                      {ngo.uploadedDocs}/{ngo.totalMandatoryDocs}
                                    </Typography>
                                  </Box>
                                  <LinearProgress
                                    variant="determinate"
                                    value={ngo.documentCompletionPercentage}
                                    sx={{
                                      height: 6,
                                      borderRadius: 3,
                                      backgroundColor: 'rgba(0,0,0,0.08)',
                                      '& .MuiLinearProgress-bar': {
                                        borderRadius: 3,
                                        backgroundColor:
                                          ngo.documentCompletionPercentage === 100
                                            ? '#4caf50'
                                            : ngo.documentCompletionPercentage >= 70
                                              ? '#ff9800'
                                              : '#f44336'
                                      }
                                    }}
                                  />
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={2}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {ngo.documentCompletionPercentage === 100 && (
                                    <CheckCircleOutlined style={{ fontSize: 16, color: '#4caf50' }} />
                                  )}
                                  <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    sx={{
                                      color:
                                        ngo.documentCompletionPercentage === 100
                                          ? '#4caf50'
                                          : ngo.documentCompletionPercentage >= 70
                                            ? '#ff9800'
                                            : '#f44336'
                                    }}
                                  >
                                    {ngo.documentCompletionPercentage}%
                                  </Typography>
                                </Box>
                              </Grid>
                            </Grid>
                          </Box>
                        ))}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Content Posting Section */}
              <Grid item xs={12} sx={{ mt: 4, mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <BulbOutlined style={{ color: '#1976d2', fontSize: 28 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                    Content Posting
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Card
                  sx={{
                    borderRadius: 3,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    border: '1px solid rgba(0,0,0,0.05)'
                  }}
                >
                  <CardContent sx={{ p: 0 }}>
                    {dashboardStats.todaysCampaigns.campaigns.length === 0 ? (
                      <Box sx={{ p: 6, textAlign: 'center' }}>
                        <BulbOutlined style={{ fontSize: 64, color: '#bdbdbd', marginBottom: 16 }} />
                        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                          No Campaigns Today
                        </Typography>
                        <Typography variant="body2" color="text.disabled">
                          No campaigns have been created today
                        </Typography>
                      </Box>
                    ) : (
                      <Box sx={{ overflowX: 'auto' }}>
                        <Box
                          sx={{
                            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                            p: 2,
                            borderBottom: '1px solid rgba(0,0,0,0.08)'
                          }}
                        >
                          <Grid container spacing={2} sx={{ fontWeight: 600, color: 'text.primary' }}>
                            <Grid item xs={12} sm={5}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Campaign Details
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                NGO Partner
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={3}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                                Status
                              </Typography>
                            </Grid>
                          </Grid>
                        </Box>

                        {dashboardStats.todaysCampaigns.campaigns.map((campaign, index) => (
                          <Box
                            key={campaign.id}
                            sx={{
                              p: 2.5,
                              borderBottom:
                                index < dashboardStats.todaysCampaigns.campaigns.length - 1 ? '1px solid rgba(0,0,0,0.05)' : 'none',
                              '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.02)',
                                transition: 'background-color 0.2s ease'
                              }
                            }}
                          >
                            <Grid container spacing={2} alignItems="center">
                              <Grid item xs={12} sm={5}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                                  <Avatar
                                    sx={{
                                      bgcolor: 'secondary.main',
                                      width: 44,
                                      height: 44,
                                      fontSize: '0.875rem'
                                    }}
                                  >
                                    <BulbOutlined style={{ fontSize: 20 }} />
                                  </Avatar>
                                  <Box>
                                    <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5, lineHeight: 1.3 }}>
                                      {campaign.title || campaign.name || 'Untitled Campaign'}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      Created today
                                    </Typography>
                                  </Box>
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={4}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <BankOutlined style={{ fontSize: 18, color: '#757575' }} />
                                  <Typography variant="body2" fontWeight={500}>
                                    {campaign.ngo_name || 'NGO not assigned'}
                                  </Typography>
                                </Box>
                              </Grid>

                              <Grid item xs={12} sm={3}>
                                <Chip
                                  label={campaign.status || 'Draft'}
                                  size="medium"
                                  sx={{
                                    backgroundColor:
                                      campaign.status === 'Live'
                                        ? '#e8f5e8'
                                        : campaign.status === 'Approved'
                                          ? '#e3f2fd'
                                          : campaign.status === 'In Review'
                                            ? '#fff3e0'
                                            : '#f5f5f5',
                                    color:
                                      campaign.status === 'Live'
                                        ? '#2e7d32'
                                        : campaign.status === 'Approved'
                                          ? '#1976d2'
                                          : campaign.status === 'In Review'
                                            ? '#f57c00'
                                            : '#666',
                                    fontWeight: 600,
                                    minWidth: 80,
                                    '& .MuiChip-label': { px: 2 }
                                  }}
                                />
                              </Grid>
                            </Grid>
                          </Box>
                        ))}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </>
          )}
        </Grid>
      )}

      <Grid item md={8} sx={{ display: { sm: 'none', md: 'block', lg: 'none' } }} />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        <Grid item xs={12} sx={{ mb: -2.25, mt: 2 }}>
          <Typography variant="h5">NGO Stats</Typography>
        </Grid>
        {isLoadingNgo ? (
          <Grid item xs={12} display="flex" justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
            <CircularProgress />
            <Typography sx={{ margin: 2 }}>Loading NGO Stats. Please wait....</Typography>
          </Grid>
        ) : (
          <>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Total" count={ngoStatsAll.totalNgos} url="/masters/ngos" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="In Review" count={ngoStatsAll.inReviewNgos} url="/masters/ngos/?alertStatus=In Review" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Pending" count={ngoStatsAll.pendingNgos} url="/masters/ngos?alertStatus=Pending" />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <AnalyticEcommerce title="Verified" count={ngoStatsAll.verifiedNgos} url="/masters/ngos?alertStatus=Verified" />
            </Grid>
          </>
        )}
      </Grid>
      <hr />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        <Grid item xs={12} sx={{ mb: -2.25 }}>
          <Typography variant="h5">Campaign Stats</Typography>
        </Grid>
        {isLoadingCampaign ? (
          <Grid item xs={12} display="flex" justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
            <CircularProgress />
            <Typography sx={{ margin: 2 }}>Loading Campaigns Stats. Please wait....</Typography>
          </Grid>
        ) : (
          <>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Total" count={campaignStatsAll.totalCampaigns} url="/masters/campaigns" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="In Review" count={campaignStatsAll.inReview} url="/masters/campaigns/?alertStatus=In Review" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Approved" count={campaignStatsAll.approved} url="/masters/campaigns/?alertStatus=Approved" />
              </Link>
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Link to="/masters/campaigns" style={{ textDecoration: 'none' }}>
                <AnalyticEcommerce title="Live" count={campaignStatsAll.live} url="/masters/campaigns/?alertStatus=Live" />
              </Link>
            </Grid>
          </>
        )}
      </Grid>
      <hr />
      <Grid container rowSpacing={4.5} columnSpacing={3}>
        {/* row 2 */}
        <Grid item xs={12} md={12} lg={12}>
          {/* <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Income Overview</Typography>
          </Grid>
        </Grid>
        <IncomeOverviewCard /> */}
          <ClusteredMapComponent
            setInReviewCount={() => {}}
            url={`${API_BASE_URL}/ngos/allNgos`}
            headerName={'NGOs Cluster Map'}
            type="NGO"
          />
        </Grid>
        <Grid item xs={12} md={6} lg={6}>
          {/* <PageViews /> */}
          {/* <HeatmapComponent url={`${API_BASE_URL}/campaigns/status/Live`} headerName={'Campaigns HeatMap'} /> */}
        </Grid>

        {/* row 2 */}
        {/* <Grid item xs={12} md={6} lg={6}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Income Overview</Typography>
          </Grid>
        </Grid>
        <IncomeOverviewCard />
      </Grid>
      <Grid item xs={12} md={6} lg={6}>
        <PageViews />
      </Grid> */}

        {/* row 3 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Recent Orders</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <OrdersList />
        </MainCard>
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <Grid container alignItems="center" justifyContent="space-between">
          <Grid item>
            <Typography variant="h5">Analytics Report</Typography>
          </Grid>
          <Grid item />
        </Grid>
        <MainCard sx={{ mt: 2 }} content={false}>
          <List sx={{ p: 0, '& .MuiListItemButton-root': { py: 2 } }}>
            <ListItemButton divider>
              <ListItemText primary="Company Finance Growth" />
              <Typography variant="h5">+45.14%</Typography>
            </ListItemButton>
            <ListItemButton divider>
              <ListItemText primary="Company Expenses Ratio" />
              <Typography variant="h5">0.58%</Typography>
            </ListItemButton>
            <ListItemButton>
              <ListItemText primary="Business Risk Cases" />
              <Typography variant="h5">Low</Typography>
            </ListItemButton>
          </List>
          <ReportChart />
        </MainCard>
      </Grid> */}

        {/* row 4 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <SaleReportCard />
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <TransactionHistory />
      </Grid> */}

        {/* row 5 */}
        {/* <Grid item xs={12} md={7} lg={8}>
        <Stack spacing={3}>
          <LabelledTasks />
          <ReaderCard />
        </Stack>
      </Grid>
      <Grid item xs={12} md={5} lg={4}>
        <AcquisitionChannels />
      </Grid> */}
      </Grid>
    </Fragment>
  );
}
