import React, { useEffect, useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  CircularProgress,
  Typography,
  Grid,
  styled,
  Chip,
  Tooltip,
  IconButton,
  FormHelperText,
  Stack
} from '@mui/material';
import { useLocation, useNavigate, useParams } from 'react-router';
import Autocomplete from '@mui/material/Autocomplete';
import CustomReactTable from 'components/CustomerDataGrid';
import { toast, ToastContainer } from 'react-toastify';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions } from 'utils/permissionUtils';
import 'react-toastify/dist/ReactToastify.css';
import { API_BASE_URL } from 'api/categories.service';
import { getNgoById } from 'api/ngos.service';
import { calculateProfileCompletion } from 'utils/permissionUtils';
import TableActions from 'components/TableActions';
import { FormattedMessage } from 'react-intl';
import useAuth from 'hooks/useAuth';
import { fetchRolesService } from './apis/role.service';
import {
  addAdminService,
  deleteAdminService,
  fetchAdminsService,
  fetchAllRoles,
  fetchRolesBytype,
  updateAdminService
} from './apis/portal-users.service';
import { BookOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import * as yup from 'yup';
import { updateUsingPatchNGO } from 'sections/apps/profiles/profile.service';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';

export default function AdminTable() {
  const { user } = useAuth();
  const [admins, setAdmins] = useState([]);
  const [roles, setRoles] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentAdmin, setCurrentAdmin] = useState(null);
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isloading, setIsLoading] = useState(false);
  const [payloadValues, setPayloadValues] = useState(null);
  const [showNameMismatchDialog, setShowNameMismatchDialog] = useState(false);
  const [initialStatus, setInitialStatus] = useState(null);
  const [ngoInfo, setNgoInfo] = useState(null);

  const [roleCounts, setRoleCounts] = useState({});
  const navigate = useNavigate();

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const memberStatus = queryParams.get('memberStatus');

  //role based acess
  const permissions = getUserPermissions(user);
  const canAdd = permissions?.Add?.StaffMembers || false;
  const canEdit = permissions?.Edit?.StaffMembers || false;
  const canDelete = permissions?.Delete?.StaffMembers || false;

  const MAX_MEMBERS = {
    NGO_Management: 1,
    NGO_Staff: 2
  };

  useEffect(() => {
    // Calculate role-based counts
    const counts = {};
    admins.forEach((admin) => {
      const roleName = admin.roleInfo?.name;
      if (roleName) {
        counts[roleName] = (counts[roleName] || 0) + 1;
      }
    });
    setRoleCounts(counts);
  }, [admins]);

  useEffect(() => {
    // fetchAdmins();
    fetchRoles();
  }, []);
  useEffect(() => {
    fetchAdmins();
  }, []);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngodata = await getNgoById(user.ngo_id, 'StaffMembersTable');
          setNgoInfo(ngodata);
          const profilepercentage = await calculateProfileCompletion(ngodata);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(0);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const fetchAdmins = async () => {
    try {
      if (user?.ngo_id) {
        const data = await fetchAdminsService(user?.ngo_id, null, 'StaffMembersTable');
        setAdmins(data);
      } else {
        const userId = user?.roleInfo?.name !== 'DR_Management' ? user?.id : null;
        const data = await fetchAdminsService(null, userId, 'StaffMembersTable');
        setAdmins(data);
      }
    } catch (error) {
      console.error('Failed to fetch Members:', error);
      toast.error('Failed to fetch Members');
    }
  };

  const fetchRoles = async () => {
    try {
      if (user?.ngo_id) {
        const response = await fetchRolesBytype(user?.ngo_id, 'StaffMembersTable');
        setRoles(response);
      } else {
        const response = await fetchRolesBytype();
        setRoles(response);
      }
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      toast.error('Failed to fetch roles');
    }
  };

  const validateRoleLimit = (roleId) => {
    const role = roles.find((r) => r.id === roleId);
    if (!role) return true;

    const roleName = role.name;
    const currentRoleCount = admins.filter((admin) => admin.roleInfo?.name === roleName).length;

    if (roleName in MAX_MEMBERS && currentRoleCount >= MAX_MEMBERS[roleName]) {
      toast.error(`You can only add up to ${MAX_MEMBERS[roleName]} members for the role ${roleName}`);
      return false;
    }

    return true;
  };

  const handleAddOrEdit = async (values, initialStatus) => {
    if (user?.ngo_id && !isEditing) {
      const isValid = validateRoleLimit(values.role_id);
      if (!isValid) return;
    }
    setLoading(true);
    try {
      if (isEditing) {
        if (
          values?.ngoInfo &&
          values.panNgoName?.trim().toLowerCase() !== values?.ngoInfo.name?.trim().toLowerCase() &&
          initialStatus === 'Inactive' &&
          values.status == 'Active'
        ) {
          setOpenDialog(false);
          setShowNameMismatchDialog(true);
          return;
        } else {
          await updateAdminService(values.id, values, 'StaffMembersTable');
          toast.success('Member updated successfully!');
        }
      } else {
        await addAdminService(values, 'StaffMembersTable');
        toast.success('Member added successfully!');
      }
      setOpenDialog(false);
      fetchAdmins();
    } catch (error) {
      //   if (error.response?.data?.message?.includes('already taken')) {
      //     toast.error('The email address is already in use. Please use a different email.');
      //   }
      if (error.response?.data?.message?.includes('We were unable to send the email')) {
        toast.success('Account created succesfully. We were unable to send the email');
        setOpenDialog(false);
      } else {
        console.error('Failed to save member:', error);
        toast.error(error.response?.data?.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const updateNameMismatchedUpdated = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        updateAdminService(payloadValues?.id, payloadValues, 'StaffMembersTable'),
        updateUsingPatchNGO(payloadValues?.ngo_id, {
          pan: payloadValues?.pan,
          panNgoName: payloadValues?.panNgoName
        })
      ]);
      toast.success('Member updated successfully!');

      setTimeout(() => {
        fetchAdmins();
      }, 1000);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to save member:', error);
      toast.error('Failed to update member' || error.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!canAdd) {
      toast.error('You do not have permission to delete member.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this  member?');
    if (confirmDelete) {
      try {
        await deleteAdminService(id, 'StaffMembersTable');
        toast.success('Member deleted successfully!');
        fetchAdmins();
      } catch (error) {
        console.error('Failed to delete member:', error);
        toast.error('Failed to delete member');
      }
    }
  };
  const handleJournalClick = async (id, name) => {
    navigate(`/masters/staff-members/view-journals/${id}?name=${name}`);
  };

  const handleDeleteFlag = (roleName) => {
    if (roleName === 'NGO_Management') {
      return false;
    }
    if (roleName === 'DR_Management') {
      return false;
    }
    return true;
  };

  const openAddDialog = () => {
    if (!canAdd) {
      toast.error('You do not have permission to Add Member.');
      return;
    }
    setCurrentAdmin({
      fullname: '',
      email: '',
      password: '',
      role_id: 1,
      point_of_contact_mobile_number: '',
      current_address: '',
      status: 'Active',
      ngo_id: user?.ngo_id
    });
    setIsEditing(false);
    setOpenDialog(true);
  };

  //   const handleDeleteIconForNGO = () => {
  //     if(usercell?.row?.original?.roleInfo?.name.startsWith('DR'))
  //   }

  const openEditDialog = (admin) => {
    setInitialStatus(admin.status);
    if (!canEdit) {
      toast.error('You do not have permission to Edit Member.');
      return;
    }
    setCurrentAdmin(admin);
    setIsEditing(true);
    setOpenDialog(true);
  };
  const columns = [
    {
      accessorKey: 'fullname',
      header: 'Full Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Button variant="text" onClick={() => openEditDialog(cell.row.original)}>
            {cell.row.original.fullname}
          </Button>
        );
      }
    },
    { accessorKey: 'email', header: 'Email', showByDefault: true },
    ...(user?.roleInfo?.name?.startsWith('DR')
      ? [
          {
            accessorKey: 'ngoInfo.name',
            header: 'Ngo Name',
            showByDefault: true,

            cell: ({ row }) => {
              const ngoName = row.original.ngoInfo?.name;

              return ngoName ? (
                <Button variant="text" onClick={() => navigate(`/masters/ngos/edit/ngoprofile/${row.original?.ngoInfo?.id}`)}>
                  {ngoName}
                </Button>
              ) : (
                <Typography variant="body" marginLeft={2}>
                  N/A
                </Typography>
              );
            }
          }
        ]
      : []),
    { accessorKey: 'roleInfo.name', header: 'Role', showByDefault: true },
    { accessorKey: 'status', header: 'Status', showByDefault: true },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      showByDefault: true,
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Action',
      cell: (cell) => {
        return (
          <Box display="flex" alignItems="center" gap={1}>
            <TableActions
              handleEditClick={openEditDialog}
              cell={cell}
              handleDeleteClick={handleDelete}
              showDelete={handleDeleteFlag(cell?.row?.original?.roleInfo?.name)}
            />
            {cell?.row?.original?.roleInfo?.name.startsWith('DR') && (
              <Tooltip title="View All Journals">
                <IconButton color="success" onClick={() => handleJournalClick(cell.row.original.id, cell.row.original.fullname)}>
                  <BookOutlined />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        );
      }
    }
  ];

  const StyledChip = styled(Chip)(({ theme }) => ({
    fontWeight: 'bold',
    fontSize: '0.9rem',
    padding: theme.spacing(1, 2)
  }));

  const RoleBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2)
  }));

  const RoleName = styled(Typography)(({ theme }) => ({
    marginRight: theme.spacing(2),
    minWidth: 100,
    fontWeight: 'bold'
  }));

  const renderRoleCounts = () => {
    return (
      <Grid container spacing={2}>
        {Object.keys(MAX_MEMBERS).map((roleName) => (
          <Grid item xs={12} sm={6} md={4} key={roleName}>
            <RoleBox>
              <RoleName variant="body1">{roleName}:</RoleName>
              <StyledChip
                label={`${roleCounts[roleName] || 0} / ${MAX_MEMBERS[roleName]}`}
                color={roleCounts[roleName] >= MAX_MEMBERS[roleName] ? 'error' : 'primary'}
                variant="outlined"
              />
            </RoleBox>
          </Grid>
        ))}
      </Grid>
    );
  };

  const addValidationSchema = () => {
    return yup.object().shape({
      fullname: yup.string().max(150, 'Full name must be at most 150 characters long').required('Full name is required'),
      email: yup
        .string()
        .email('Invalid email address')
        .max(160, 'Email must be at most 160 characters long')
        .required('Email is required'),
      role_id: yup.number().required('Please select a role'),
      password: yup.string().max(20, 'Password must be at most 20 characters long').required('Please enter a password'),
      point_of_contact_mobile_number: yup
        .string()
        .max(12, 'Contact number must be at most 12 characters long')
        .required('Contact number is required'),
      status: yup.string().required('Please select a status')
    });
  };

  const updateValidateSchema = () => {
    return yup.object().shape({
      fullname: yup.string().max(150, 'Full name must be at most 5 characters long').required('Full name is required'),
      email: yup.string().email('Invalid email address').max(160, 'Email must be at most 50 characters long').required('Email is required'),
      role_id: yup.number().required('Please select a role'),
      point_of_contact_mobile_number: yup
        .string()
        .max(12, 'Contact number must be at most 12 characters long')
        .required('Contact number is required'),
      status: yup.string().required('Please select a status')
    });
  };

  return (
    <div>
      {/* <Box sx={{ marginBottom: 2 }}>
        {user?.ngo_id && profileCompletePercentage && profileCompletePercentage !== '100' && <ProfileCard />}
      </Box> */}
      {/* {user?.ngo_id && (
        <Box sx={{ marginBottom: 2, padding: 2, backgroundColor: '#f9f9f9', borderRadius: 1 }}>
          
          {renderRoleCounts()}
        </Box>
      )} */}

      {user?.ngo_id && ngoInfo && profileCompletePercentage < 100 && (
        <Grid item xs={12} sx={{ marginBottom: 2 }}>
          <ProfileCard ngoInfo={ngoInfo} profileCompletePercentage={profileCompletePercentage} />
        </Grid>
      )}
      <CustomerTable
        data={admins}
        columns={columns}
        modalToggler={openAddDialog}
        category={<FormattedMessage id="StaffMember" />}
        statusFiltervalue={memberStatus}
      />
      <Dialog
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <Formik
          initialValues={{
            ...currentAdmin
          }}
          validationSchema={!isEditing ? addValidationSchema : updateValidateSchema}
          onSubmit={async (values) => {
            await handleAddOrEdit(values, initialStatus);
            setPayloadValues(values);
          }}
        >
          {({ values, handleSubmit, handleChange, handleBlur, setFieldValue, touched, errors }) => {
            return (
              <form autoComplete="off" onSubmit={handleSubmit}>
                <DialogTitle>{isEditing ? 'Edit Member' : 'Add Member'}</DialogTitle>
                <DialogContent>
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      <TextField
                        autoFocus
                        margin="dense"
                        label="Full Name"
                        type="text"
                        fullWidth
                        variant="outlined"
                        name="fullname"
                        onChange={(e) => {
                          const onlyLetters = e.target.value.replace(/[^a-zA-Z\s]/g, ''); // allow letters + space
                          handleChange({
                            target: {
                              name: 'fullname',
                              value: onlyLetters
                            }
                          });
                        }}
                        onBlur={handleBlur}
                        value={values.fullname}
                        error={Boolean(touched.fullname && errors.fullname)}
                        helperText={touched.fullname && errors.fullname}
                        required
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        margin="dense"
                        label="Email"
                        type="email"
                        fullWidth
                        variant="outlined"
                        name="email"
                        disabled={currentAdmin?.roleInfo?.name === 'NGO_Management'}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.email}
                        error={Boolean(touched.email && errors.email)}
                        helperText={touched.email && errors.email}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        margin="dense"
                        label="Password"
                        type="password"
                        fullWidth
                        variant="outlined"
                        name="password"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.password}
                        error={Boolean(touched.password && errors.password)}
                        helperText={touched.password && errors.password}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Autocomplete
                        options={roles}
                        disabled={isEditing}
                        getOptionLabel={(option) => option.name}
                        value={roles.find((role) => role.id === values.role_id) || null}
                        onChange={(event, newValue) => setFieldValue('role_id', newValue?.id)}
                        onBlur={handleBlur}
                        isOptionEqualToValue={(option, value) => option.id === value.id} // Custom equality check
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            name="role_id"
                            label="Role"
                            disabled={isEditing}
                            error={Boolean(touched.role_id && errors.role_id)}
                            helperText={touched.role_id && errors.role_id}
                            required
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        margin="dense"
                        label="Contact Mobile"
                        type="text"
                        fullWidth
                        variant="outlined"
                        name="point_of_contact_mobile_number"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.point_of_contact_mobile_number}
                        error={Boolean(touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number)}
                        helperText={touched.point_of_contact_mobile_number && errors.point_of_contact_mobile_number}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        margin="dense"
                        label="Current Address"
                        type="text"
                        fullWidth
                        variant="outlined"
                        name="current_address"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.current_address}
                        error={Boolean(touched.current_address && errors.current_address)}
                        helperText={touched.current_address && errors.current_address}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl fullWidth margin="dense" variant="outlined" error={Boolean(touched.status && errors.status)}>
                        <InputLabel>Status</InputLabel>
                        <Select name="status" value={values.status} onChange={handleChange} onBlur={handleBlur} label="Status">
                          <MenuItem value="Active">Active</MenuItem>
                          <MenuItem value="Inactive">Inactive</MenuItem>
                        </Select>
                        {touched.status && errors.status && <FormHelperText>{errors.status}</FormHelperText>}
                      </FormControl>
                    </Grid>
                  </Grid>
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setOpenDialog(false)} color="primary">
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    color="primary"
                    disabled={loading || (user?.ngo_id && profileCompletePercentage < 100)}
                    startIcon={loading && <CircularProgress size={20} />}
                  >
                    {isEditing ? 'Update' : 'Add'}
                  </Button>
                </DialogActions>
              </form>
            );
          }}
        </Formik>
      </Dialog>

      <Dialog
        open={showNameMismatchDialog}
        onClose={() => {}}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
        disableEscapeKeyDown
        disableBackdropClick
        slotProps={{
          backdrop: {
            style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' }
          }
        }}
      >
        <DialogTitle>NGO Name Mismatch with PAN Records</DialogTitle>
        <DialogContent>
          <Typography>
            The name provided for the NGO does not match the name associated with the verified PAN. For accuracy, we will update it to match
            the official PAN record
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            variant="text"
            onClick={() => {
              setShowNameMismatchDialog(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="text"
            onClick={async () => {
              await updateNameMismatchedUpdated();
              setShowNameMismatchDialog(false);
            }}
          >
            {isloading ? <CircularProgress size={20} color="primary" /> : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />
    </div>
  );
}
