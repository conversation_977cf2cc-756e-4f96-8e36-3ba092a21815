import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress';

export default function BankDetailsConfirmDialog({ open, onClose, onSubmit, details, loading }) {
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (loading) return;
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          onClose();
        }
      }}
      maxWidth="50%"
      PaperProps={{
        sx: {
          top: 0,
          position: 'absolute',
          borderRadius: '16px',
          width: '100%',
          maxWidth: '600px'
        }
      }}
    >
      <DialogTitle>Confirm Bank Details</DialogTitle>
      <DialogContent>
        <Box mb={2}>
          <Typography variant="body1" gutterBottom>
            Please double-check your details before submitting.
          </Typography>
          <Typography variant="body2" color="error" gutterBottom>
            Once you submit your account information — it cannot be changed after verification starts.
            <br />
            If any detail is incorrect, it may delay payouts or require support intervention.
          </Typography>
          <Typography variant="body2" color="success.main" gutterBottom>
            ✅ Make sure the bank account number, IFSC code, name, and all details are 100% correct.
          </Typography>
        </Box>
        <Divider sx={{ mb: 2 }} />
        <Box
          sx={{
            backgroundColor: '#f5f7fa',
            borderRadius: 2,
            p: 2,
            border: '1px solid #e0e0e0'
          }}
        >
          {[
            { label: 'Beneficiary Name', value: details.beneficiary_name },
            { label: 'Bank Name', value: details.bank_name },
            { label: 'Branch Name', value: details.branch_name },
            { label: 'Branch Address', value: details.branch_address },
            { label: 'Account Number', value: details.account_number },
            { label: 'IFSC Code', value: details.ifsc_code }
          ].map((item, idx) => (
            <Box
              key={item.label}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                py: 1,
                borderBottom: idx !== 5 ? '1px solid #e0e0e0' : 'none'
              }}
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                {item.label}:
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {item.value}
              </Typography>
            </Box>
          ))}
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          px: 3, // Matches the default horizontal padding of DialogContent
          pb: 2, // Matches bottom padding
          justifyContent: 'flex-end'
        }}
      >
        <Button onClick={onClose} color="secondary" variant="outlined" disabled={loading}>
          Cancel
        </Button>
        <Button onClick={onSubmit} color="primary" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={22} color="inherit" /> : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
