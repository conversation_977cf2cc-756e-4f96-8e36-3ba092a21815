import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  CircularProgress,
  Button,
  Chip,
  Avatar,
  IconButton,
  Select,
  MenuItem,
  FormControl
} from '@mui/material';
import { PieChart } from '@mui/x-charts/PieChart';
import { LineChart } from '@mui/x-charts/LineChart';
import { ArrowLeftOutlined, FundOutlined, HeartOutlined, TrophyOutlined, UserOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';

// Services
import { getSingleCampaignStats } from 'pages/masters/apis/campaign-event-stats.service';

// Components
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';

const COLORS = ['#ffae5f', '#ff9800', '#f57c00', '#e65100', '#bf360c', '#8d6e63'];

const CampaignReportPage = () => {
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const [campaignStats, setCampaignStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timePeriod, setTimePeriod] = useState('1Y');

  useEffect(() => {
    if (campaignId) {
      fetchCampaignStats();
    }
  }, [campaignId]);

  const fetchCampaignStats = async () => {
    try {
      setLoading(true);
      const response = await getSingleCampaignStats(campaignId);

      if (response?.success) {
        setCampaignStats(response.data);
      } else {
        toast.error('Failed to fetch campaign details');
      }
    } catch (error) {
      console.error('Error fetching campaign stats:', error);
      toast.error('An error occurred while fetching campaign details');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `₹${amount?.toLocaleString() || 0}`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'completed':
        return 'primary';
      case 'draft':
        return 'warning';
      case 'paused':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading campaign details...</Typography>
      </Box>
    );
  }

  if (!campaignStats) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary">
          Campaign not found
        </Typography>
        <Button onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          Go Back
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate(-1)} sx={{ mr: 2 }}>
          <ArrowLeftOutlined />
        </IconButton>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Campaign Detailed Report
        </Typography>
      </Box>

      {/* Campaign Info */}
      <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
            {campaignStats.campaignInfo?.name}
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Category
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {campaignStats.campaignInfo?.category}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Status
              </Typography>
              <Chip label={campaignStats.campaignInfo?.status} size="small" color={getStatusColor(campaignStats.campaignInfo?.status)} />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Created Date
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {dayjs(campaignStats.campaignInfo?.createdAt).format('DD/MM/YYYY')}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                End Date
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {dayjs(campaignStats.campaignInfo?.campaignEndDate).format('DD/MM/YYYY')}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Campaign KPIs */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <FundOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {formatCurrency(campaignStats.totalFundGoal)}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Fund Goal
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <TrophyOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {formatCurrency(campaignStats.fundCollected?.amount)}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Collected ({campaignStats.fundCollected?.percentage?.toFixed(1)}%)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <HeartOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {campaignStats.livesImpacted?.toLocaleString() || 0}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Lives Impacted
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <UserOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {campaignStats.topDonors?.length || 0}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Total Donors
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Fund Collection Trend */}
        <Grid item xs={12} md={12}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#ffae5f' }}>
                  Fund Collection Trend
                </Typography>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <Select
                    value={timePeriod}
                    onChange={(e) => setTimePeriod(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(255, 174, 95, 0.3)'
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#ffae5f'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#ffae5f'
                      }
                    }}
                  >
                    <MenuItem value="3M">3 Months</MenuItem>
                    <MenuItem value="6M">6 Months</MenuItem>
                    <MenuItem value="1Y">1 Year</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              {campaignStats.fundCollectionTrend &&
              campaignStats.fundCollectionTrend[timePeriod] &&
              campaignStats.fundCollectionTrend[timePeriod].length > 0 ? (
                <LineChart
                  xAxis={[
                    {
                      scaleType: 'point',
                      data: campaignStats.fundCollectionTrend[timePeriod].map((item) => item.month)
                    }
                  ]}
                  series={[
                    {
                      data: campaignStats.fundCollectionTrend[timePeriod].map((item) => item.amount),
                      label: 'Amount Collected',
                      color: '#ffae5f'
                    }
                  ]}
                  width={500}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No trend data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Lives Impacted Breakdown */}
        {/* <Grid item xs={12} md={6}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                Lives Impacted Breakdown
              </Typography>
              {campaignStats.livesImpactedBreakdown && campaignStats.livesImpactedBreakdown.length > 0 ? (
                <PieChart
                  series={[
                    {
                      data: campaignStats.livesImpactedBreakdown.map((item, index) => ({
                        id: index,
                        value: item.livesImpacted,
                        label: item.categoryName,
                        color: COLORS[index % COLORS.length]
                      }))
                    }
                  ]}
                  width={400}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No breakdown data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid> */}
      </Grid>

      {/* Top Donors Table */}
      <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
            Top Donors
          </Typography>

          {campaignStats.topDonors && campaignStats.topDonors.length > 0 ? (
            <CustomerTableWithoutFilter
              data={campaignStats.topDonors}
              columns={[
                {
                  accessorKey: 'donorName',
                  header: 'Donor Name',
                  cell: ({ cell }) => (
                    <Typography variant="body2" fontWeight={600}>
                      {cell.row.original.donorName}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'email',
                  header: 'Email',
                  cell: ({ cell }) => (
                    <Typography variant="body2" color="text.secondary">
                      {cell.row.original.email}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'totalDonated',
                  header: 'Total Donated',
                  cell: ({ cell }) => (
                    <Typography variant="body2" fontWeight={600} sx={{ color: '#ffae5f' }}>
                      {formatCurrency(cell.row.original.totalDonated)}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'donationCount',
                  header: 'Donations',
                  cell: ({ cell }) => (
                    <Chip
                      label={cell.row.original.donationCount}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 174, 95, 0.1)',
                        color: '#ffae5f',
                        fontWeight: 600
                      }}
                    />
                  )
                },
                {
                  accessorKey: 'lastDonationDate',
                  header: 'Last Donation',
                  cell: ({ cell }) => (
                    <Typography variant="body2">{dayjs(cell.row.original.lastDonationDate).format('DD/MM/YYYY')}</Typography>
                  )
                }
              ]}
              category="Top Donors"
            />
          ) : (
            <Typography variant="body2" sx={{ color: 'text.secondary', mt: 2 }}>
              No donor data available yet.
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default CampaignReportPage;
