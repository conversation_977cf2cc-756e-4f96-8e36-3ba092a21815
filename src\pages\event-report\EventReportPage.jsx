import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Card, CardContent, Grid, Typography, CircularProgress, Button, Chip, Avatar, IconButton } from '@mui/material';
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart';
import { Bar<PERSON>hart } from '@mui/x-charts/BarChart';
import { ArrowLeftOutlined, CalendarOutlined, ClockCircleOutlined, TeamOutlined, TrophyOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';

// Services
import { getSingleEventStats } from 'pages/masters/apis/event-stats.service';

// Components
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';

const COLORS = ['#ffae5f', '#ff9800', '#f57c00', '#e65100', '#bf360c', '#8d6e63'];
const GENDER_COLORS_EXTENDED = [
  '#6FB1FC',
  '#FFB6C1', // Female - Light Pink
  '#C0C0C0', // Other - Neutral Gray
  '#B39DDB', // Transgender / Genderqueer - Lavender
  '#AED581', // Prefer not to say - Soft Green
  '#FFF176' // Intersex or Custom - Soft Yellow
];
const EventReportPage = () => {
  const { eventId } = useParams();
  const navigate = useNavigate();
  const [eventStats, setEventStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (eventId) {
      fetchEventStats();
    }
  }, [eventId]);

  const fetchEventStats = async () => {
    try {
      setLoading(true);
      const response = await getSingleEventStats(eventId);

      if (response?.success) {
        setEventStats(response.data);
      } else {
        toast.error('Failed to fetch event details');
      }
    } catch (error) {
      console.error('Error fetching event stats:', error);
      toast.error('An error occurred while fetching event details');
    } finally {
      setLoading(false);
    }
  };

  const formatHours = (hours) => {
    return `${hours?.toLocaleString() || 0} hrs`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'ongoing':
        return 'primary';
      case 'upcoming':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getEventTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'physical':
        return 'primary';
      case 'online':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading event details...</Typography>
      </Box>
    );
  }

  if (!eventStats) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary">
          Event not found
        </Typography>
        <Button onClick={() => navigate(-1)} sx={{ mt: 2 }}>
          Go Back
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate(-1)} sx={{ mr: 2 }}>
          <ArrowLeftOutlined />
        </IconButton>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Event Detailed Report
        </Typography>
      </Box>

      {/* Event Info */}
      <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
            {eventStats.eventInfo?.name}
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Category
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {eventStats.eventInfo?.category}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Type
              </Typography>
              <Chip label={eventStats.eventInfo?.eventType} size="small" color={getEventTypeColor(eventStats.eventInfo?.eventType)} />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Status
              </Typography>
              <Chip label={eventStats.eventInfo?.status} size="small" color={getStatusColor(eventStats.eventInfo?.status)} />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Event Date
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {dayjs(eventStats.eventInfo?.eventDate).format('DD/MM/YYYY')}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Start Time
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {eventStats.eventInfo?.eventStartTime}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                End Time
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {eventStats.eventInfo?.eventEndTime}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Location
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                {eventStats.eventInfo?.location || 'Online Event'}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Event KPIs */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <ClockCircleOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {formatHours(eventStats.totalVolunteerHours)}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Volunteer Hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <TrophyOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {formatHours(eventStats.totalImpactHours)}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Impact Hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <TeamOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {eventStats.rsvpStatistics?.actualParticipants || 0}
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Participants
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                <CalendarOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', mb: 1 }}>
                {eventStats.rsvpStatistics?.conversionRate?.toFixed(1) || 0}%
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                Conversion Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Gender Demographics */}
        <Grid item xs={12} md={6}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                Gender Demographics
              </Typography>
              {eventStats.demographicData && eventStats.demographicData.length > 0 ? (
                <PieChart
                  series={[
                    {
                      data: eventStats.demographicData.map((item, index) => ({
                        id: index,
                        value: item.count,
                        label: item.gender,
                        color: GENDER_COLORS_EXTENDED[index % GENDER_COLORS_EXTENDED.length]
                      }))
                    }
                  ]}
                  width={400}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No demographic data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* RSVP vs Participation */}
        <Grid item xs={12} md={6}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                RSVP vs Participation
              </Typography>
              {eventStats.rsvpStatistics ? (
                <BarChart
                  xAxis={[
                    {
                      scaleType: 'band',
                      data: ['RSVP Yes', 'RSVP No', 'Participated', 'No Show']
                    }
                  ]}
                  series={[
                    {
                      data: [
                        eventStats.rsvpStatistics.rsvpYesCount,
                        eventStats.rsvpStatistics.rsvpNoCount,
                        eventStats.rsvpStatistics.actualParticipants,
                        eventStats.rsvpStatistics.acknowledgedButNoShow
                      ],
                      label: 'Count',
                      color: '#ffae5f'
                    }
                  ]}
                  width={500}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No RSVP data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Participants Table */}
      {eventStats.participantsList && eventStats.participantsList.length > 0 && (
        <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
              Event Participants
            </Typography>
            <CustomerTableWithoutFilter
              data={eventStats.participantsList}
              columns={[
                {
                  accessorKey: 'participantName',
                  header: 'Participant Name',
                  cell: ({ cell }) => (
                    <Typography variant="body2" fontWeight={600}>
                      {cell.row.original.participantName}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'email',
                  header: 'Email',
                  cell: ({ cell }) => (
                    <Typography variant="body2" color="text.secondary">
                      {cell.row.original.email}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'gender',
                  header: 'Gender',
                  cell: ({ cell }) => <Typography variant="body2">{cell.row.original.gender}</Typography>
                },
                {
                  accessorKey: 'volunteerHours',
                  header: 'Volunteer Hours',
                  cell: ({ cell }) => (
                    <Typography variant="body2" fontWeight={600} sx={{ color: '#ffae5f' }}>
                      {formatHours(cell.row.original.volunteerHours)}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'impactHours',
                  header: 'Impact Hours',
                  cell: ({ cell }) => (
                    <Typography variant="body2" fontWeight={600} sx={{ color: '#ffae5f' }}>
                      {formatHours(cell.row.original.impactHours)}
                    </Typography>
                  )
                },
                {
                  accessorKey: 'rsvpStatus',
                  header: 'RSVP Status',
                  cell: ({ cell }) => (
                    <Chip
                      label={cell.row.original.rsvpStatus}
                      size="small"
                      color={cell.row.original.rsvpStatus === 'Yes' ? 'success' : 'default'}
                      sx={{
                        backgroundColor: cell.row.original.rsvpStatus === 'Yes' ? 'rgba(255, 174, 95, 0.1)' : undefined,
                        color: cell.row.original.rsvpStatus === 'Yes' ? '#ffae5f' : undefined,
                        fontWeight: 600
                      }}
                    />
                  )
                },
                {
                  accessorKey: 'scannedAt',
                  header: 'Check-in Time',
                  cell: ({ cell }) => (
                    <Typography variant="body2">
                      {cell.row.original.scannedAt ? dayjs(cell.row.original.scannedAt).format('HH:mm') : 'Not checked in'}
                    </Typography>
                  )
                }
              ]}
              category="Event Participants"
            />
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default EventReportPage;
