import axiosServices from 'utils/axios_node';
import mapboxgl from 'mapbox-gl';
export const sessionToken = '0af7b610-9cae-42a1-892e-c6a1df102ec5';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const DOCUMENTS_BASE_URL = `${import.meta.env.VITE_APP_DOUMENT_URL}`;
//import this from env i was having some issue so i hardcoded here
export const MAPBOX_ACCESS_TOKEN = 'pk.eyJ1IjoiZGlwcy0xIiwiYSI6ImNtNHFsbW5pczE1NzYyaXExNnd4NG9nOGsifQ.h9YgD9xMh60on9FRuDFTTA';

export const getAllCampaigns = async (status, ngo_id, pageName = '', page = 1, limit = 10, portalUserId = null, globalFilter) => {
  const statusParam = `status/${status}`;
  const queryParams = new URLSearchParams();
  if (ngo_id) queryParams.append('ngoId', ngo_id);
  if (pageName) queryParams.append('pageName', pageName);
  if (page) queryParams.append('page', page);
  if (limit) queryParams.append('limit', limit);
  if (portalUserId) queryParams.append('portalUserId', portalUserId);
  if (globalFilter) queryParams.append('searchTerm', globalFilter);

  const url = `${API_BASE_URL}/campaigns/${statusParam}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await axiosServices.get(url);
  return response?.data;
};

export const getCampaigns = async (ngoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns`, {
    params: ngoId ? { ngoId } : {}
  });
  return response?.data;
};

export const getCampaignsFromCategoryIds = async (categoryIds) => {
  const response = await axiosServices.post(`${API_BASE_URL}/campaigns/by-categories`, {
    categoryIds
  });
  return response?.data;
};

export const getNGOSBySearchTerm = async (search = '', status, portalUserId = null, pageNumber, limit) => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/ngos/search`, {
      params: { search, status, portalUserId, pageNumber, limit }
    });
    return response?.data;
  } catch (error) {
    console.error('Error fetching NGOs:', error);
  }
};
export const getCampaignById = async (campaignId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns/${campaignId}`);
  return response?.data;
};

export const addCampaign = async (campaignData, pageName = '') => {
  const response = await axiosServices.post(`${API_BASE_URL}/campaigns?pageName=${pageName}`, campaignData);
  return response?.data;
};

export const updateCampaign = async (campaignId, campaignData, pageName = '') => {
  const response = await axiosServices.put(`${API_BASE_URL}/campaigns/${campaignId}?pageName=${pageName}`, campaignData);
  return response?.data;
};
export const patchCampaign = async (campaignId, campaignData, pageName = '') => {
  const response = await axiosServices.patch(`${API_BASE_URL}/campaigns/${campaignId}?pageName=${pageName}`, campaignData);
  return response?.data;
};

export const deleteCampaign = async (campaignId, pageName = '') => {
  const response = await axiosServices.delete(`${API_BASE_URL}/campaigns/${campaignId}?pageName=${pageName}`);
  return response?.data;
};

export const getPlaceSuggestions = async (searchQuery) => {
  const response = await axiosServices.get(`https://api.mapbox.com/search/searchbox/v1/suggest`, {
    params: {
      q: searchQuery,
      language: 'en',
      country: 'in',
      session_token: sessionToken,
      access_token: MAPBOX_ACCESS_TOKEN
    }
  });
  return response;
};

export const getPlaceDetails = async (suggestion) => {
  const response = await axiosServices.get(`https://api.mapbox.com/search/searchbox/v1/retrieve/${suggestion.mapbox_id}`, {
    params: {
      session_token: sessionToken,
      access_token: MAPBOX_ACCESS_TOKEN
    }
  });
  return {
    response,
    latitude: response.data.features[0].geometry.coordinates[0],
    longitude: response.data.features[0].geometry.coordinates[1]
  };
};
export const getStateFromCoordinates = async (latitude, longitude) => {
  const accessToken = 'pk.eyJ1IjoiYmh1c2hhbjY1NiIsImEiOiJjbTJ6cXA4OGgwZHIxMm1xMm81aDNyazE1In0.Zm3xPsoxU9RNU9t468gX9Q';
  const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${accessToken}`;

  try {
    const response = await axiosServices.get(url);
    const features = response.data.features;

    const stateFeature = features.find((feature) => feature.place_type.includes('region'));
    return stateFeature ? stateFeature.text : null;
  } catch (error) {
    console.error('Error fetching state:', error);
    return null;
  }
};

export const getMileStones = async (campaignId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaign-milestones/getCampaignMilestones?campaignId=${campaignId}`);
  return response?.data;
};

export const addMileStone = async (payload, pageName = '') => {
  const response = await axiosServices.post(`${API_BASE_URL}/campaign-milestones?pageName=${pageName}`, payload);
  return response?.data;
};

export const updateMileStone = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/campaign-milestones/${id}`, payload);
  return response?.data;
};

export const assignfeaturedToCampaigns = async (campaignId, featureMode) => {
  try {
    const response = await axiosServices.patch(
      `${API_BASE_URL}/campaigns/mark/featured?campaignId=${campaignId}&featureMode=${featureMode}`
    );
    return response;
  } catch (error) {
    throw new Error('Failed to mark as featured ');
  }
};

//donation
export const getKindDonationByCampaignId = async (campaignId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kind-donations?campaign_id=${campaignId}`);
  return response?.data;
};
export const getCampaignBySearchTerm = async (query, ngo_id, portalUserId, type) => {
  try {
    const params = new URLSearchParams();
    params.append('search', query);
    if (ngo_id) {
      params.append('ngo_id', ngo_id);
    }
    if (portalUserId) {
      params.append('portalUserId', portalUserId);
    }
    if (type) {
      params.append('type', type);
    }

    const response = await axiosServices.get(`${API_BASE_URL}/campaigns/search?${params.toString()}`);
    return response.data;
  } catch (error) {
    throw new Error('Failed to fetch Campaigns');
  }
};
