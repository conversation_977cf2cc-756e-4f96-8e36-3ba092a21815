import { getNgoById } from 'api/ngos.service';
import { getDocumentsMasterList, getNGODocumentsList } from 'pages/masters/apis/documents.service';
import { getAllBankDetails } from 'sections/apps/profiles/account/tabs.service';
export function getUserPermissions(user) {
  if (typeof user === 'string') {
    const userInfo = JSON.parse(user);
    return userInfo.roleInfo ? JSON.parse(userInfo.roleInfo.permissions) : {};
  }
  return user?.roleInfo ? user?.roleInfo?.permissions : {};
}

export function formatAmountIndian(value) {
  if (value >= ********) return (value / ********).toFixed(1).replace(/\.0$/, '') + 'Cr'; // Crore
  if (value >= 100000) return (value / 100000).toFixed(1).replace(/\.0$/, '') + 'L'; // Lakh
  if (value >= 1000) return (value / 1000).toFixed(1).replace(/\.0$/, '') + 'K'; // Thousand
  return value.toString(); // Below 1000, show raw value
}

export const convertMBToBytes = (mb) => {
  const bytes = mb * (1024 * 1024);
  return bytes;
};

export const getConvertedFileName = (fileName) => {
  if (fileName) {
    const splitFileName = fileName.replace(/\s/g, '-').toLowerCase().split('.');
    const newSplitFileName = `${splitFileName[0]}${Date.now()}.${splitFileName[splitFileName.length - 1]}`;
    return newSplitFileName;
  }
};

export const calculateProfileCompletion = async (ngoData) => {
  const requiredFields = [
    'name',
    'email',
    'pan',
    'point_of_contact_name',
    'point_of_contact_mobile_number',
    'ngo_type',
    'current_address',
    'latitude',
    'longitude',
    'pincode',
    'state',
    'country',
    'vision',
    'mission',
    'about_us',
    'date_of_establishment',
    'tagline'
  ];
  const completedFields = requiredFields.filter((field) => ngoData[field] && ngoData[field].toString().trim() !== '');
  const fieldCompletionPercentage = (completedFields.length / requiredFields.length) * 40;

  //docs percentage
  const documents = await getDocumentsMasterList('NGO', ngoData.ngo_type);

  const mandatoryDocuments = documents.filter((document) => document.mandatory === 'yes');

  const docsSubmitted = await getNGODocumentsList(ngoData.id);

  const filteredDocs = docsSubmitted.filter((doc) => doc.approval_status === 'Pending' || doc.approval_status === 'Approved');

  const submittedDocIds = filteredDocs.map((doc) => doc.documentId);

  const mandatoryDocsCompleted = mandatoryDocuments.filter((doc) => submittedDocIds.includes(doc.id));

  const bankDetails = await getAllBankDetails(ngoData?.id);
  const bankDetailsPercentage = bankDetails?.length > 0 ? 10 : 0;

  const documentCompletionPercentage = ngoData?.ngo_type
    ? mandatoryDocuments.length > 0
      ? (mandatoryDocsCompleted.length / mandatoryDocuments.length) * 50
      : 0 // If there are no mandatory documents, set percentage to 0
    : 0; // If ngo_type doesn't exist, set percentage to 0

  const profileCompletePercentage = Number(fieldCompletionPercentage + documentCompletionPercentage + bankDetailsPercentage).toFixed();

  return parseInt(profileCompletePercentage);
};
export const getSessionStorageItem = (key) => {
  try {
    const value = localStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('Error reading from sessionStorage', error);
    return null;
  }
};
export const checkRequiredFieldsCompletion = async (ngoData) => {
  const basinInfo = [
    'name',
    'email',
    'pan',
    'point_of_contact_name',
    'point_of_contact_mobile_number',
    'ngo_type',
    'current_address',
    'latitude',
    'longitude',
    'pincode',
    'state',
    'country'
  ];

  const aboutInfo = ['vision', 'mission', 'about_us', 'date_of_establishment'];

  const documents = await getDocumentsMasterList('NGO', ngoData.ngo_type);

  const mandatoryDocuments = documents.filter((document) => document.mandatory === 'yes');

  const docsSubmitted = await getNGODocumentsList(ngoData.id);

  const submittedDocIds = docsSubmitted.map((doc) => doc.documentId);

  const allMandatoryDocsUploaded = mandatoryDocuments.every((doc) => submittedDocIds.includes(doc.id));

  const bankDetails = await getAllBankDetails(ngoData?.id);

  const isGroupCompleted = (fields) => {
    return fields.every((field) => ngoData[field] && ngoData[field].toString().trim() !== '');
  };

  const basinInfoComplted = isGroupCompleted(basinInfo);
  const aboutInfoCompleted = isGroupCompleted(aboutInfo);

  return {
    basinInfoComplted: basinInfoComplted,
    aboutInfoCompleted: aboutInfoCompleted,
    allMandatoryDocsUploaded,
    isbankDetailsAdded: bankDetails.length > 0
  };
};

export const getDocumentUploadStatus = async (ngoId, ngoType) => {
  const documents = await getDocumentsMasterList('NGO', ngoType);
  const mandatoryDocuments = documents.filter((document) => document.mandatory === 'yes');

  const docsSubmitted = await getNGODocumentsList(ngoId);
  const submittedDocIds = docsSubmitted.map((doc) => doc.documentId);

  return {
    uploadedDocs: submittedDocIds.length,
    totalMandatoryDocs: mandatoryDocuments.length
  };
};

export const getInitials = (name) => {
  if (!name) return '';
  const nameParts = name.trim().split(' ');
  const initials = nameParts
    .slice(0, 2) // Take the first two words
    .map((part) => part.charAt(0).toUpperCase()) // Get the first character of each word
    .join('');
  return initials;
};

export const getCommentText = (description) => {
  const isJson = description?.startsWith('{');
  if (isJson) {
    try {
      const parsedDescription = JSON.parse(description);
      if (parsedDescription?.blocks) {
        return parsedDescription.blocks.map((block) => block.text).join('<br />');
      }
    } catch (error) {
      console.error('Failed to parse JSON:', error);
    }
  }
  return description;
};

export const generateSlug = (name) => {
  return name.toLowerCase().trim().split(' ').join('-');
};

export const formatIndianNumber = (num) => {
  if (num === null || num === undefined || num === '') return '';

  // Ensure number type
  const number = Number(num);
  if (isNaN(number)) return num;

  // Split integer and decimal parts
  const [integerPart, decimalPart] = number.toString().split('.');

  // Apply Indian numbering format to integer part
  const lastThree = integerPart.substring(integerPart.length - 3);
  const otherNumbers = integerPart.substring(0, integerPart.length - 3);
  const formattedInteger = otherNumbers !== '' ? otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + ',' + lastThree : lastThree;

  // Return with decimals (if any)
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const hasValidPriorities = (images) => images.length > 0 && images.every((img) => typeof img.priority === 'number');

export const sortByPriority = (images) => [...images].sort((a, b) => a.priority - b.priority);
