import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { GoogleMap, useJsApiLoader, Marker } from '@react-google-maps/api';
import axios from 'axios';
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  CircularProgress,
  Divider,
  InputAdornment,
  Alert,
  Tooltip
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getLoggedInNGOInfo, updateNGO, addNGO, updateUsingPatchNGO } from '../profile.service';
import MainCard from 'components/MainCard';
import { getSessionStorageItem } from 'utils/permissionUtils';
import { getAllNgoTypes } from 'api/ngotypes.service';
import LocationMap from 'components/LocationMap';
import UnsavedChangesDialog from './UnsavedChangesDialog';
import { sendVerificationEmail, sendverificationNumber, verifyOtp } from './tabs.service';
import { CheckCircleOutlined } from '@ant-design/icons';
// You'll need to set up an environment variable for your Google Maps API key
const GOOGLE_MAPS_API_KEY = 'AIzaSyDAlmZjT27PfFPOFsUVixpv6jPPtwkRVcs';

export default function TabProfile() {
  const [isEditing, setIsEditing] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingLocate, setIsLoadingLocate] = useState(false);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [markerPosition, setMarkerPosition] = useState({ lat: 22.199166, lng: 78.476681 });
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);

  const userInfo = getSessionStorageItem('user');
  const isNgo = userInfo.ngo_id;

  //to track the unsaved changes
  const [isValuesChanged, setValuesChanged] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigationPath, setPendingNavigationPath] = useState(null);
  const [otpDialogOpen, setOtpDialogOpen] = useState(false);
  const [otp, setOtp] = useState('');

  const { newNgoId } = useParams();
  const navigate = useNavigate();
  const [ngoTypes, setNgoTypes] = useState([]);
  const [pan, setPan] = useState(null);
  const [ngoDetails, setNgoDetails] = useState({
    name: '',
    point_of_contact_name: '',
    email: '',
    point_of_contact_mobile_number: '',
    pan: '',
    ngo_status: 'New',
    last_status: 'New',
    country: '',
    state: '',
    pincode: '',
    current_address: '',
    ngo_type: null,
    latitude: 22.199166,
    longitude: 78.476681
  });

  const [error, setError] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [editableLocation, setEditableLocation] = useState(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerifyingNumber, setIsVerifyingNumber] = useState(false);
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false);

  const [resendTimer, setResendTimer] = useState(0);
  const [resendIntervalId, setResendIntervalId] = useState(null);

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY
  });

  const startResendTimer = () => {
    setResendTimer(120); // 2 minutes = 120 seconds
    const intervalId = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(intervalId);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    setResendIntervalId(intervalId);
  };

  const fetchNgoTypes = async () => {
    try {
      const response = await getAllNgoTypes('BasicInfo');
      setNgoTypes(response);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to fetch documents');
    }
  };

  useEffect(() => {
    fetchNgoTypes();
    fetchngoDetails();
  }, []);

  const fetchLocationDetailsFromPincode = async (pincode) => {
    setIsPincodeLoading(true);

    try {
      const response = await axios.get(`https://api.postalpincode.in/pincode/${pincode}`);
      if (response.data && response.data.length > 0 && response.data instanceof Array && response.data?.[0]?.PostOffice?.length > 0) {
        const postOfficeData = response.data?.[0]?.PostOffice?.[0];
        setNgoDetails((prev) => ({
          ...prev,
          country: postOfficeData.Country || '',
          state: postOfficeData.State || '',
          place_name: postOfficeData.Block || postOfficeData.District || ''
        }));
        setValuesChanged(true);
      } else {
        toast.warning('Could not find details for the provided pincode.');
        setNgoDetails((prev) => ({ ...prev, country: '', state: '', place_name: '' }));
        setValuesChanged(true);
      }
    } catch (error) {
      console.error('Error fetching location details from pincode:', error);
      toast.error('Failed to fetch location details for the pincode.');
      setNgoDetails((prev) => ({ ...prev, country: '', state: '', place_name: '' }));
      setValuesChanged(true);
    } finally {
      setIsPincodeLoading(false);
    }
  };

  const fetchngoDetails = async () => {
    setIsLoading(true);

    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setNgoDetails({
          ...response
        });
        setPan(response?.pan);
        setMarkerPosition({ lat: response.latitude, lng: response.longitude });
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      setPan(response?.pan);
      setNgoDetails({
        ...response,
        country: response?.country || '',
        state: response?.state || '',
        pincode: response?.pincode || ''
      });
      setMarkerPosition({ lat: response.latitude, lng: response.longitude });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to fetch user details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyEmail = async () => {
    setIsVerifying(true); // Show loader
    try {
      const response = await sendVerificationEmail(ngoDetails.email); // API Call
      if (response.status) {
        toast.success('A verification email has been sent. Please check your inbox and spam folder.');
      } else {
        toast.error('Failed to send verification email. Try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsVerifying(false); // Hide loader
    }
  };

  const handleVerifyNumber = async (point_of_contact_mobile_number) => {
    setIsVerifyingNumber(true); // Show loader
    try {
      const response = await sendverificationNumber(point_of_contact_mobile_number); // API Call
      if (response.status) {
        toast.success('A OTP has been sent. Please check your mobile phone');
        setOtpDialogOpen(true);
      } else {
        toast.error('Failed to send otp. Try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsVerifyingNumber(false); // Hide loader
    }
  };

  const verifyNumber = async (point_of_contact_mobile_number, otp) => {
    setIsVerifyingOTP(true);
    try {
      const response = await verifyOtp(point_of_contact_mobile_number, otp); // API Call
      if (response.status) {
        const ngoResp = await updateUsingPatchNGO(ngoDetails?.id, { isNumberVerified: 'yes' });
        toast.success('Mobile number verified successfully.');
        if (ngoResp) {
          setTimeout(() => {
            fetchngoDetails();
          }, 2000);
        }
        setOtpDialogOpen(false);
      } else {
        toast.error('Failed to send otp. Try again.');
      }
    } catch (error) {
      toast.error('An error occurred. Please try again.');
    } finally {
      setOtpDialogOpen(false); // Hide loader
      setIsVerifyingOTP(false);
      setOtp('');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'pincode' && value.length === 6) {
      fetchLocationDetailsFromPincode(value);
    }

    setNgoDetails((prev) => ({ ...prev, [name]: value }));
    setValuesChanged(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!ngoDetails.latitude) {
      window.alert(`Please "Pin Your Address" on the map`);
      return;
    }
    if (!['C', 'A', 'T'].includes(ngoDetails.pan[3].toUpperCase())) {
      toast.error("The 4th character of PAN must be 'C', 'A', or 'T'");
      return;
    }

    try {
      const newNgoDetails = { ...ngoDetails };
      if (isNgo) {
        const response = await updateUsingPatchNGO(userInfo?.ngo_id, newNgoDetails, 'BasicInfo');
        toast.success('Basic information updated successfully');
        setValuesChanged(false);
        return;
      }
      if (newNgoId) {
        const ngoRecord = await updateUsingPatchNGO(newNgoId, newNgoDetails, 'BasicInfo');
        toast.success('Basic information updated successfully');
        setValuesChanged(false);

        setTimeout(() => {
          navigate(`/masters/ngos/edit/personal/${ngoRecord.id}`);
        }, 1000);
        return;
      }
      newNgoDetails.pageName = 'BasicInfo';
      const { ngoRecord } = await addNGO(newNgoDetails);
      navigate(`/masters/ngos/edit/personal/${ngoRecord.id}`);
      toast.success('Ngo Added successfully');
    } catch (error) {
      console.error('Error updating user details:', error);
      toast.error(error?.message || 'Failed to update NGO Basic Information');
    }
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    fetchngoDetails();
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const handleSearchSubmit = async (searchQuery) => {
    if (!searchQuery) {
      window.alert('Please enter address');
      return;
    }
    const extraParts = [ngoDetails?.pincode, ngoDetails?.state, ngoDetails?.country].filter(Boolean); // filters out undefined or empty

    const fullQuery = `${searchQuery}, ${extraParts.join(', ')}`;

    setIsLoadingLocate(true);

    try {
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: fullQuery }, (results, status) => {
        if (status === 'OK') {
          setSuggestions(results);

          setError('');
        } else {
          setError('Failed to retrieve suggestions. Please try again.');
        }
        // if (results[0]) {
        //   handleSuggestionClick(results[0]);
        // }
        setIsLoadingLocate(false);
      });
    } catch (error) {
      setError('Failed to retrieve suggestions. Please try again.');
      setIsLoadingLocate(false);
    } finally {
      // handleSuggestionClick(suggestions[0]);
    }
  };

  const handleSuggestionClick = async (suggestion) => {
    setIsLoadingSuggestion(true);
    try {
      const { lat, lng } = suggestion.geometry.location;
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ location: { lat: lat(), lng: lng() } }, (results, status) => {
        if (status === 'OK') {
          const addressComponents = results[0].address_components;
          const current_address = results[0].formatted_address || '';
          setEditableLocation({
            latitude: lat(),
            longitude: lng()
            // current_address: current_address
          });

          setNgoDetails((prev) => ({
            ...prev,
            latitude: lat(),
            longitude: lng()
            // current_address: current_address
          }));
          setMarkerPosition({ lat: lat(), lng: lng() });
          setIsDialogOpen(true);
        } else {
          setError('Failed to retrieve location details. Please try again.');
        }
        setIsLoadingSuggestion(false);
      });
    } catch (error) {
      setError('Failed to retrieve location details. Please try again.');
      setIsLoadingSuggestion(false);
    }
  };

  const handleConfirm = () => {
    setIsDialogOpen(false);
  };

  const handleMarkerDragEnd = (event) => {
    setMarkerPosition(event.latLng);
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ location: event.latLng }, (results, status) => {
      if (status === 'OK') {
        const addressComponents = results[0].address_components;

        const current_address = results[0].formatted_address || '';

        setNgoDetails((prev) => ({
          ...prev,
          latitude: event.latLng.lat(),
          longitude: event.latLng.lng()
          // current_address: current_address
        }));
      }
    });
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress />
        <Typography sx={{ mt: 2 }}>Loading Basic Information....</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2} justifyContent={'center'} alignItems={'center'}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="NGO's legal name"
                    name="name"
                    required
                    value={ngoDetails.name}
                    onChange={handleInputChange}
                    disabled={userInfo?.roleInfo?.name === 'DR_Management' || userInfo?.roleInfo?.name === 'DR_Staff' ? false : true}
                    inputProps={{ maxLength: 180 }}
                  />
                </Grid>
                {/* Contact Person */}
                {/* <Grid item xs={12} sm={1}></Grid> */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Contact person's name"
                    name="point_of_contact_name"
                    required
                    value={ngoDetails.point_of_contact_name}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    inputProps={{ maxLength: 60 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="NGO's Email address*"
                    name="email"
                    value={ngoDetails.email}
                    onChange={handleInputChange}
                    disabled={isNgo || !isEditing}
                    inputProps={{ maxLength: 180 }}
                    InputProps={{
                      endAdornment: ngoDetails.email ? (
                        <InputAdornment position="end">
                          {ngoDetails.isEmailVerified === 'yes' ? (
                            <Tooltip title="Email verified">
                              <CheckCircleOutlined style={{ color: 'green', fontSize: '20px' }} />
                            </Tooltip>
                          ) : (
                            <Button variant="contained" color="primary" size="small" onClick={handleVerifyEmail} disabled={isVerifying}>
                              {isVerifying ? <CircularProgress size={20} color="inherit" /> : 'Verify Email'}
                            </Button>
                          )}
                        </InputAdornment>
                      ) : null
                    }}
                  />
                </Grid>
                {/* <Grid item xs={12} sm={1}></Grid> */}
                {/* Mobile number */}
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Mobile number"
                    required
                    name="point_of_contact_mobile_number"
                    value={ngoDetails.point_of_contact_mobile_number}
                    onChange={handleInputChange}
                    // disabled={!isEditing}
                    disabled={userInfo?.roleInfo?.name === 'DR_Management' || userInfo?.roleInfo?.name === 'DR_Staff' ? false : true}
                    inputProps={{ maxLength: 10 }}
                    InputProps={{
                      endAdornment: ngoDetails.point_of_contact_mobile_number ? (
                        <InputAdornment position="end">
                          {ngoDetails.isNumberVerified === 'yes' ? (
                            <Tooltip title="Contact number verified">
                              <CheckCircleOutlined style={{ color: 'green', fontSize: '20px' }} />
                            </Tooltip>
                          ) : (
                            <Button
                              variant="contained"
                              color="primary"
                              size="small"
                              onClick={() => {
                                if (ngoDetails?.point_of_contact_mobile_number) {
                                  handleVerifyNumber(ngoDetails?.point_of_contact_mobile_number);
                                  startResendTimer();
                                }
                              }}
                              disabled={isVerifyingNumber || resendTimer > 0}
                            >
                              {isVerifyingNumber ? (
                                <CircularProgress size={20} color="inherit" />
                              ) : resendTimer > 0 ? (
                                `Resend in ${resendTimer}s`
                              ) : (
                                'Verify Contact Number'
                              )}
                            </Button>
                          )}
                        </InputAdornment>
                      ) : null
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="NGO's PAN number"
                    name="pan"
                    required
                    value={ngoDetails.pan}
                    onChange={handleInputChange}
                    // disabled={!isEditing}
                    disabled={!(!pan || userInfo?.roleInfo?.name === 'DR_Management' || userInfo?.roleInfo?.name === 'DR_Staff')}
                    inputProps={{ maxLength: 10 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    options={ngoTypes || []}
                    getOptionLabel={(option) => option?.name || ''}
                    disabled={!isEditing}
                    value={ngoTypes.find((option) => option.name === ngoDetails?.ngo_type) || null}
                    onChange={(event, newValue) => {
                      setValuesChanged(true);
                      setNgoDetails({ ...ngoDetails, ngo_type: newValue?.name || '' });
                    }}
                    renderInput={(params) => <TextField required {...params} label="NGO Type" fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Grid container md={12} spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Pincode"
                        name="pincode"
                        value={ngoDetails.pincode || ''}
                        // disabled={ngoDetails.pincode}
                        onChange={handleInputChange}
                        InputProps={{
                          endAdornment: isPincodeLoading ? (
                            <InputAdornment position="end">
                              <CircularProgress size={20} />
                            </InputAdornment>
                          ) : null
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="City"
                        disabled
                        name="place_name"
                        value={ngoDetails.place_name || ''}
                        // disabled={ngoDetails.place_name}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        // disabled={ngoDetails.state}
                        label="State"
                        disabled
                        name="state"
                        value={ngoDetails.state || ''}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Country"
                        name="country"
                        disabled
                        value={ngoDetails.country || ''}
                        // disabled={ngoDetails.country}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Address"
                    required
                    name="current_address"
                    value={ngoDetails?.current_address}
                    onChange={handleInputChange}
                    InputLabelProps={{ shrink: true }}
                    disabled={!isEditing}
                    // inputProps={{ maxLength: 300 }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Button
                            variant="contained"
                            color="primary"
                            onClick={() => handleSearchSubmit(ngoDetails?.current_address)}
                            disabled={isLoadingLocate}
                          >
                            {isLoadingLocate ? <CircularProgress size={20} color="inherit" /> : 'Pin Your Address'}
                          </Button>
                        </InputAdornment>
                      )
                    }}
                    //  endAdornment={<InputAdornment position="end">kg</InputAdornment>}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Grid container md={12} spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Longitude"
                        disabled
                        name="longitude"
                        value={ngoDetails.longitude || ''}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        label="Latitude"
                        name="latitude"
                        disabled
                        value={ngoDetails.latitude || ''}
                        onChange={handleInputChange}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={6}></Grid>
                <Grid item xs={12} style={{ textAlign: 'left' }}>
                  {error && <Typography color="error">{error}</Typography>}
                  {suggestions.length > 0 && (
                    <>
                      {' '}
                      <Divider
                        sx={{
                          borderBottomWidth: 2 // Thickness (default is 1)
                        }}
                      />
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: 'text.secondary',
                          fontWeight: 500,
                          fontSize: '0.85rem',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px',
                          mb: 1,
                          mt: 1
                        }}
                      >
                        Location Suggestions
                      </Typography>
                    </>
                  )}

                  {suggestions.length > 0 && (
                    <Box style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '10px', marginTop: '10px' }}>
                      {suggestions.map((suggestion, index) => (
                        <Chip
                          key={index}
                          label={suggestion.formatted_address}
                          onClick={() => !isLoadingSuggestion && handleSuggestionClick(suggestion)}
                          clickable
                          color="primary"
                          disabled={isLoadingSuggestion}
                        />
                      ))}
                      {isLoadingSuggestion && (
                        <Box style={{ display: 'flex', justifyContent: 'center', marginTop: '10px' }}>
                          <CircularProgress size={24} />
                        </Box>
                      )}
                    </Box>
                  )}
                </Grid>
              </Grid>
              <Grid item xs={12} sm={12}>
                <Grid
                  container
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between', // Distribute buttons with space between them
                    alignItems: 'center', // Vertically center the buttons
                    gap: 2,
                    marginTop: 2
                  }}
                >
                  {/* Save Button aligned to the left */}
                  <Grid item>
                    <Button size="large" type="submit" variant="contained" color="primary">
                      Save
                    </Button>
                  </Grid>

                  {/* Back and Next buttons aligned to the right */}
                  <Grid item>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        const path = newNgoId ? `/masters/ngos/edit/ngoprofile/${newNgoId}` : '/apps/profiles/account/ngoprofile';

                        if (isValuesChanged) {
                          setPendingNavigationPath(path); // Store path before showing dialog
                          setShowUnsavedDialog(true);
                          return;
                        }
                        navigate(path);
                      }}
                      sx={{ mr: 1 }}
                    >
                      Back
                    </Button>

                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        const path = newNgoId ? `/masters/ngos/edit/personal/${newNgoId}` : '/apps/profiles/account/personal';

                        if (isValuesChanged) {
                          setPendingNavigationPath(path); // Store path before showing dialog
                          setShowUnsavedDialog(true);
                          return;
                        }
                        navigate(path);
                      }}
                    >
                      Next
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </form>

            <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
              <Alert color="error">
                Note: If you click 'Next' or 'Back', any unsaved changes will be discarded. Be sure to click 'Save' first.
              </Alert>
            </Grid>
          </Grid>
        </Grid>
         <Dialog open={isDialogOpen} onClose={handleDialogClose} maxWidth="lg" fullWidth>
          <DialogTitle>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                textAlign: 'left',
                color: 'primary',
                pb: 2,
                borderBottom: '1px solid',
                borderColor: 'divider'
              }}
            >
              Please zoom and place the pin as accurately as possible at your exact location📍
            </Typography>
          </DialogTitle>
          <DialogContent
            sx={{
              py: 4,
              px: 3,
              backgroundColor: 'background.default'
            }}
          >
            {isLoaded ? (
              <Box
                sx={{
                  height: 300,
                  borderRadius: 2,
                  overflow: 'hidden',
                  mb: 3,
                  boxShadow: 3
                }}
              >
                <LocationMap center={markerPosition} onMarkerDragEnd={handleMarkerDragEnd} />
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 300,
                  mb: 3
                }}
              >
                <CircularProgress />
              </Box>
            )}

            <Box
              sx={{
                px: 2,
                py: 3,
                borderRadius: 2,
                backgroundColor: 'background.paper',
                boxShadow: 1
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  mb: 2,
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  pb: 1
                }}
              >
                Address Details
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    <TextField
                    fullWidth
                    multiline
                    rows={2}
                    label="Flat, House no., Building, Company, Apartment, Area, Street, Sector, Village, Landmark"
                    required
                    name="current_address"
                    value={ngoDetails?.current_address}
                    onChange={handleInputChange}
                    InputLabelProps={{ shrink: true }}
                    disabled={!isEditing}
                    // inputProps={{ maxLength: 300 }}
                    
                    //  endAdornment={<InputAdornment position="end">kg</InputAdornment>}
                  />
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body1">
                    <strong>Country:</strong> {ngoDetails.country || 'N/A'}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body1">
                    <strong>State:</strong> {ngoDetails.state || 'N/A'}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body1">
                    <strong>City:</strong> {ngoDetails.place_name || 'N/A'}
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body1">
                    <strong>Pincode:</strong> {ngoDetails.pincode || 'N/A'}
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: 'center',
              py: 2,
              backgroundColor: 'background.default'
            }}
          >
            <Button
              onClick={handleDialogClose}
              color="secondary"
              sx={{
                textTransform: 'none',
                borderRadius: 3,
                fontSize: '1rem',
                px: 3
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleConfirm();
                setSuggestions([]);
              }}
              variant="contained"
              color="primary"
              sx={{
                textTransform: 'none',
                borderRadius: 3,
                fontSize: '1rem',
                px: 3,
                ml: 2
              }}
            >
              Confirm and Proceed
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={otpDialogOpen}
          onClose={(event, reason) => {
            if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
              setOtpDialogOpen(false);
            }
          }}
          maxWidth="50%"
          PaperProps={{
            sx: {
              top: 0,
              position: 'absolute',
              borderRadius: '16px',
              width: '100%',
              maxWidth: '600px'
            }
          }}
        >
          <DialogTitle>Enter OTP</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="OTP"
              variant="outlined"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              margin="normal"
              inputProps={{ maxLength: 6 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOtpDialogOpen(false)} color="secondary">
              Cancel
            </Button>
            <Button
              onClick={async () => {
                verifyNumber(ngoDetails?.point_of_contact_mobile_number, otp);
              }}
              color="primary"
              variant="contained"
            >
              {isVerifyingOTP ? <CircularProgress size={24} /> : 'Submit'}
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>

      <UnsavedChangesDialog
        showUnsavedDialog={showUnsavedDialog}
        setShowUnsavedDialog={setShowUnsavedDialog}
        pendingNavigationPath={pendingNavigationPath}
      />

      <ToastContainer autoClose={6000} />
    </Card>
  );
}
