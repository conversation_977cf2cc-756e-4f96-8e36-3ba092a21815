import axiosServices from 'utils/axios_node';

export const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const BASE_URL = API_BASE_URL;
export const IMAGE_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/uploads`;

export const getLoggedInNGOInfo = async (ngoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngos/${ngoId}`);
  return response?.data;
};

export const patchNGOInfo = async (userId, payload, pageName = '') => {
  const response = await axiosServices.put(`${API_BASE_URL}/ngos/${userId}?pageName=${pageName}`, payload);
  return response?.data;
};

export const updateNGO = async (userId, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/ngos/${userId}`, payload);
  return response?.data;
};
export const updateUsingPatchNGO = async (userId, payload, pageName = '') => {
  const url = pageName ? `${API_BASE_URL}/ngos/docs/${userId}?pageName=${pageName}` : `${API_BASE_URL}/ngos/docs/${userId}`;
  const response = await axiosServices.patch(url, payload);
  return response?.data;
};

export const addNGO = async (payload) => {
  const response = await axiosServices.post(`${API_BASE_URL}/ngos`, payload);
  return response?.data;
};

export const patchLocation = async (userId, payload) => {
  const response = await axiosServices.patch(`${API_BASE_URL}/ngos/docs/${userId}`, payload);
  return response?.data;
};

export const deleteNGOCategories = async (userId) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/ngo-category/ngos/${userId}`);
  return response?.data;
};

export const insertCategories = async (payload, pageName) => {
  const response = await axiosServices.post(`${API_BASE_URL}/ngo-category?pageName=${pageName}`, payload);
  return response?.data;
};
export const getNgoCategory = async (ngoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/ngo-category?ngoId=${ngoId}`);
  return response?.data;
};

export const addDocument = async (payload, pageName = '') => {
  const result = await axiosServices.post(`${API_BASE_URL}/document-users?pageName=${pageName}`, payload);
  return result?.data;
  // return true;
};

export const updateDocument = async (id, payload) => {
  const result = await axiosServices.put(`${API_BASE_URL}/document-users/${id}`, payload);
  return result?.data;
  // return true;
};
