import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const fetchTransactionsService = async (ngoId, page = 1, limit = 10) => {
  const queryParams = new URLSearchParams();
  if (ngoId) queryParams.append('ngoId', ngoId);
  if (page) queryParams.append('page', page);
  if (limit) queryParams.append('limit', limit);
  const url = `${API_BASE_URL}/transactions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await axiosServices.get(url);
  return response.data;
};

export const deleteTransactionService = async (id) => {
  const response = await axiosServices.delete(`${API_BASE_URL}/transactions/${id}`);
  return response.data;
};
