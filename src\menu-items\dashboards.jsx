// third-party
import { FormattedMessage } from 'react-intl';

// project-imports
import { handlerCustomerDialog } from 'api/customer';
import { NavActionType } from 'config';

// assets
import BuildOutlined from '@ant-design/icons/BuildOutlined';
import CalendarOutlined from '@ant-design/icons/CalendarOutlined';
import CustomerServiceOutlined from '@ant-design/icons/CustomerServiceOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import ShoppingCartOutlined from '@ant-design/icons/ShoppingCartOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import AppstoreAddOutlined from '@ant-design/icons/AppstoreAddOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import LinkOutlined from '@ant-design/icons/LinkOutlined';
import { DashboardOutlined } from '@ant-design/icons';

// type

// icons
const icons = {
  DashboardOutlined,
  CalendarOutlined,
  CustomerServiceOutlined,
  MessageOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreAddOutlined,
  FileTextOutlined,
  PlusOutlined,
  LinkOutlined
};

// ==============================|| MENU ITEMS - APPLICATIONS ||============================== //
const user = JSON.parse(localStorage.getItem('user')) || null;
let items = [];
if (user?.roleInfo?.name === "DR_Management") {
  items.push({
    id: 'analytics',
    title: 'Dashboard',
    type: 'item',
    url: '/dashboard/analytics',
    icon: icons.DashboardOutlined,

    breadcrumbs: false
  });
}
if (user?.roleInfo?.name !== "DR_Management") {
  items.push({
    id: 'default',
    title: 'Dashboard',
    type: 'item',
    url: '/dashboard/default',
    icon: icons.DashboardOutlined,

    breadcrumbs: false
  });
}
items.push({
    id: 'campaigns-statistics',
    title: 'Campaign Statistics',
    type: 'item',
    url: '/dashboard/campaigns-statistics',
    icon: icons.DashboardOutlined,

    breadcrumbs: false
  },{
    id: 'events-statistics',
    title: 'Event Statistics',
    type: 'item',
    url: '/dashboard/events-statistics',
    icon: icons.DashboardOutlined,

    breadcrumbs: false
  });
const dashboards = {
  id: 'group-dashboard',
  title: 'Dashboard',
  type: 'group',
  icon: icons.DashboardOutlined,
  children: items
};

export default dashboards;
