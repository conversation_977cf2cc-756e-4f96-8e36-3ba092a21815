import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  TextField,
  Typography,
  Autocomplete,
  Alert,
  Tabs,
  Tab,
  Divider,
  Chip,
  Avatar
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import { generateReport, REPORT_BASE_URL } from '../masters/apis/reports.service';
import { getNGOSBySearchTerm, getCampaignBySearchTerm } from 'api/campaigns.service';
import { getAllCategories, getCategoryById } from 'api/categories.service';
import { fetchBucketsService } from '../masters/apis/buckets.service';
import { MobileDatePicker } from '@mui/x-date-pickers';
import useAuth from 'hooks/useAuth';
import { getNgoCategory } from 'sections/apps/profiles/profile.service';
import { getNgoById } from 'api/ngos.service';
import {
  FileExcelOutlined,
  DownloadOutlined,
  FundOutlined,
  CalendarOutlined,
  DollarOutlined,
  AuditOutlined,
  BarChartOutlined
} from '@ant-design/icons';

// Import Excel Reports service
import {
  generateCampaignFinancialReport,
  generateEventAttendanceReport,
  generatePlatformRevenueReport,
  generateTransactionLogReport,
  generateAuditLogReport,
  downloadFile
} from '../masters/apis/excel-reports.service';

// Import Individual Reports service
import { generateCampaignDonorReport, generateEventVolunteerReport } from '../masters/apis/individual-reports.service';

const REPORT_TYPES = [
  { value: 'ngos', label: 'NGOs' },
  { value: 'campaigns', label: 'Campaigns' },
  { value: 'causes', label: 'Causes' },
  { value: 'buckets', label: 'Buckets' }
];

const STATUS_OPTIONS = [
  { value: 'Confirmed', label: 'Confirmed' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Failed', label: 'Failed' }
];

// Excel Reports Configuration
const DONATION_TYPES = [
  { value: 'all', label: 'All Donation Types' },
  { value: 'ngo', label: 'Direct NGO Donations' },
  { value: 'campaign', label: 'Campaign Donations' },
  { value: 'genericViaMoney', label: 'Generic Bucket Donations' }
];

const AUDIT_STATUS_OPTIONS = [
  { value: 'all', label: 'All Statuses' },
  { value: 'captured', label: 'Successful Transactions' },
  { value: 'failed', label: 'Failed Transactions' }
];

const LOCATION_OPTIONS = [
  { value: null, label: 'All Locations' },
  { value: true, label: 'Within Maharashtra' },
  { value: false, label: 'Outside Maharashtra' }
];

export default function ReportsPage() {
  const { user } = useAuth();

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Legacy Reports State
  const [reportType, setReportType] = useState('ngos');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [options, setOptions] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [status, setStatus] = useState('Confirmed');
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Excel Reports State
  const [excelLoading, setExcelLoading] = useState(false);
  const [excelError, setExcelError] = useState('');
  const [excelSuccess, setExcelSuccess] = useState('');

  // Excel Reports Date Filters
  const [excelStartDate, setExcelStartDate] = useState(null);
  const [excelEndDate, setExcelEndDate] = useState(null);

  // Audit Log Filters
  const [donationType, setDonationType] = useState('all');
  const [auditStatus, setAuditStatus] = useState('all');
  const [isDonorWithinMaharashtra, setIsDonorWithinMaharashtra] = useState(null);
  const [isNgoWithinMaharashtra, setIsNgoWithinMaharashtra] = useState(null);

  // Individual Reports State
  const [individualLoading, setIndividualLoading] = useState(false);
  const [individualError, setIndividualError] = useState('');
  const [individualSuccess, setIndividualSuccess] = useState('');
  const [individualReportType, setIndividualReportType] = useState('campaign'); // 'campaign' or 'event'
  const [individualSearchTerm, setIndividualSearchTerm] = useState('');
  const [individualSearchLoading, setIndividualSearchLoading] = useState(false);
  const [individualSearchOptions, setIndividualSearchOptions] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [individualStartDate, setIndividualStartDate] = useState(null);
  const [individualEndDate, setIndividualEndDate] = useState(null);

  // Fetch Causes or Buckets automatically when reportType changes
  useEffect(() => {
    const fetchOptions = async () => {
      setSearchLoading(true);
      setError('');
      setSuccess('');
      setOptions([]);
      setSelectedIds([]);
      try {
        if (reportType === 'causes') {
          let categoryData;
          if (user?.ngo_id) {
            // Fetch categories for this NGO
            const data = await getNgoCategory(user.ngo_id);
            if (data?.length > 0) {
              categoryData = await getCategoryById(data[0].category_id);
            }
            // data is an array of objects with category_id
            setOptions((data || []).map((item) => ({ label: `${categoryData.name}`, id: item.category_id })));
            setSelectedIds((data || []).map((item) => item.category_id));
          } else {
            const data = await getAllCategories();
            setOptions((data || []).map((cause) => ({ label: cause.name, id: cause.id })));
          }
        } else if (reportType === 'buckets') {
          const data = await fetchBucketsService();
          setOptions((data || []).map((bucket) => ({ label: bucket.name, id: bucket.id })));
        } else if (reportType === 'ngos') {
          if (user?.ngo_id) {
            const ngoData = await getNgoById(user?.ngo_id);
            setOptions([{ label: `${ngoData.name}`, id: ngoData.id }]);
            setSelectedIds([ngoData.id]);
          }
        }
      } catch (err) {
        setError('Failed to fetch options.');
      } finally {
        setSearchLoading(false);
      }
    };
    if (reportType === 'causes' || reportType === 'buckets' || (reportType === 'ngos' && user?.ngo_id)) {
      fetchOptions();
    } else {
      setOptions([]);
      setSelectedIds([]);
    }
  }, [reportType, user?.ngo_id]);

  // Reset state when report type changes
  const handleReportTypeChange = (e) => {
    setReportType(e.target.value);
    setOptions([]);
    setSelectedIds([]);
    setSearchTerm('');
    setError('');
    setSuccess('');
  };

  // Search handlers for each type
  const handleSearch = async () => {
    setSearchLoading(true);
    setError('');
    setSuccess('');
    setOptions([]);
    try {
      if (reportType === 'ngos') {
        const data = await getNGOSBySearchTerm(searchTerm, 'Verified');
        setOptions((data || []).map((ngo) => ({ label: ngo.name, id: ngo.id })));
      } else if (reportType === 'campaigns') {
        const data = await getCampaignBySearchTerm(searchTerm, user?.ngo_id);
        setOptions((data || []).map((c) => ({ label: c.name, id: c.id })));
      }
    } catch (err) {
      setError('Failed to fetch options.');
    } finally {
      setSearchLoading(false);
    }
  };

  // Build payload for report
  const buildPayload = () => {
    const payload = {};
    if (reportType === 'ngos') {
      if (user?.ngo_id) {
        payload.ngoIds = [user.ngo_id];
      } else {
        payload.ngoIds = selectedIds;
      }
    }
    if (reportType === 'campaigns') payload.campaignIds = selectedIds;
    if (reportType === 'causes') {
      if (user?.ngo_id) {
        payload.causeIds = selectedIds;
      } else {
        payload.causeIds = selectedIds;
      }
    }
    if (reportType === 'buckets') payload.bucketIds = selectedIds;
    if (startDate) payload.startDate = dayjs(startDate).format('YYYY-MM-DD');
    if (endDate) payload.endDate = dayjs(endDate).format('YYYY-MM-DD');
    if (status) payload.status = status;
    return payload;
  };

  // Generate report handler
  const handleGenerateReport = async () => {
    setGenerating(true);
    setError('');
    setSuccess('');
    try {
      const payload = buildPayload();
      if (!payload[Object.keys(payload).find((k) => k.endsWith('Ids'))]?.length) {
        setError('Please select at least one item.');
        setGenerating(false);
        return;
      }
      if (!payload.startDate || !payload.endDate) {
        setError('Please select both start and end dates.');
        setGenerating(false);
        return;
      }
      const res = await generateReport(payload);
      if (res && res.url) {
        // Join with REPORT_BASE_URL
        const fullUrl = `${REPORT_BASE_URL}${res.url}`;
        // Download the file
        const link = document.createElement('a');
        link.href = fullUrl;
        link.setAttribute('download', 'report.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setSuccess('Report generated and download started!');
      } else {
        setSuccess('Report generated!');
      }
    } catch (err) {
      setError('Failed to generate report.');
    } finally {
      setGenerating(false);
    }
  };

  // Excel Reports Handlers
  const handleExcelReport = async (reportFunction, ...args) => {
    setExcelLoading(true);
    setExcelError('');
    setExcelSuccess('');

    try {
      const response = await reportFunction(...args);

      if (response?.success && response?.data?.downloadUrl) {
        downloadFile(response.data.downloadUrl, response.data.fileName);
        setExcelSuccess(`Report generated successfully: ${response.data.fileName}`);
      } else {
        throw new Error(response?.message || 'Failed to generate report');
      }
    } catch (err) {
      setExcelError(err.message || 'Failed to generate report');
    } finally {
      setExcelLoading(false);
    }
  };

  const formatDateForAPI = (date) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : null;
  };

  const handleCampaignFinancialReport = () => {
    const startDate = formatDateForAPI(excelStartDate);
    const endDate = formatDateForAPI(excelEndDate);
    handleExcelReport(generateCampaignFinancialReport, user?.ngo_id || null, startDate, endDate);
  };

  const handleEventAttendanceReport = () => {
    const startDate = formatDateForAPI(excelStartDate);
    const endDate = formatDateForAPI(excelEndDate);
    handleExcelReport(generateEventAttendanceReport, user?.ngo_id || null, startDate, endDate);
  };

  const handlePlatformRevenueReport = () => {
    handleExcelReport(generatePlatformRevenueReport);
  };

  const handleTransactionLogReport = () => {
    if (user?.ngo_id) {
      const startDate = formatDateForAPI(excelStartDate);
      const endDate = formatDateForAPI(excelEndDate);
      handleExcelReport(generateTransactionLogReport, user.ngo_id, startDate, endDate);
    }
  };

  const handleAuditLogReport = () => {
    const filters = {
      donationType,
      status: auditStatus,
      ngoId: user?.ngo_id || null,
      isDonorWithinMaharashtra,
      isNgoWithinMaharashtra,
      startDate: formatDateForAPI(excelStartDate),
      endDate: formatDateForAPI(excelEndDate)
    };
    handleExcelReport(generateAuditLogReport, filters);
  };

  // Individual Reports Handlers
  const handleIndividualSearch = async () => {
    if (!individualSearchTerm.trim()) return;

    setIndividualSearchLoading(true);
    setIndividualError('');

    try {
      const response = await getCampaignBySearchTerm(individualSearchTerm, user?.ngo_id || null, null, individualReportType);

      if (response) {
        const formattedOptions = response.map((item) => ({
          id: item.id,
          label: item.name || item.title,
          value: item
        }));
        setIndividualSearchOptions(formattedOptions);
      } else {
        setIndividualSearchOptions([]);
        setIndividualError('No results found');
      }
    } catch (error) {
      console.error('Error searching:', error);
      setIndividualError('Failed to search. Please try again.');
      setIndividualSearchOptions([]);
    } finally {
      setIndividualSearchLoading(false);
    }
  };

  const handleIndividualReportGenerate = async () => {
    if (!selectedItem) {
      setIndividualError('Please select an item first');
      return;
    }

    setIndividualLoading(true);
    setIndividualError('');
    setIndividualSuccess('');

    try {
      const startDate = individualStartDate ? dayjs(individualStartDate).format('YYYY-MM-DD') : null;
      const endDate = individualEndDate ? dayjs(individualEndDate).format('YYYY-MM-DD') : null;

      let response;
      if (individualReportType === 'campaign') {
        response = await generateCampaignDonorReport(selectedItem.id, startDate, endDate);
      } else {
        response = await generateEventVolunteerReport(selectedItem.id, startDate, endDate);
      }

      if (response?.success && response?.data?.downloadUrl) {
        downloadFile(response.data.downloadUrl, response.data.fileName);
        setIndividualSuccess(`Report generated successfully: ${response.data.fileName}`);

        // Reset form
        setSelectedItem(null);
        setIndividualSearchTerm('');
        setIndividualSearchOptions([]);
      } else {
        throw new Error(response?.message || 'Failed to generate report');
      }
    } catch (error) {
      console.error('Error generating individual report:', error);
      setIndividualError(error.message || 'Failed to generate report');
    } finally {
      setIndividualLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box maxWidth={800} mt={4}>
        <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
          📊 Reports Dashboard
        </Typography>

        <Card>
          <CardContent>
            <Tabs
              value={tabValue}
              onChange={(e, newValue) => setTabValue(newValue)}
              sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
            >
              <Tab
                label="Excel Reports"
                icon={<FileExcelOutlined />}
                iconPosition="start"
                sx={{ textTransform: 'none', fontWeight: 600 }}
              />
              <Tab
                label="Individual Reports"
                icon={<FundOutlined />}
                iconPosition="start"
                sx={{ textTransform: 'none', fontWeight: 600 }}
              />
              <Tab
                label="Legacy Reports"
                icon={<BarChartOutlined />}
                iconPosition="start"
                sx={{ textTransform: 'none', fontWeight: 600 }}
              />
            </Tabs>

            {/* Excel Reports Tab */}
            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#ffae5f', fontWeight: 600 }}>
                  📈 Professional Excel Reports
                </Typography>

                {/* Date Range Filters */}
                <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                      📅 Date Range Filter (Optional)
                    </Typography>
                    <Grid container spacing={3} alignItems="center">
                      <Grid item xs={12} sm={4}>
                        <MobileDatePicker
                          label="Start Date"
                          value={excelStartDate}
                          format="DD/MM/YYYY"
                          onChange={setExcelStartDate}
                          renderInput={(params) => <TextField {...params} fullWidth />}
                          // slotProps={{
                          //   textField: {
                          //     helperText: 'Leave empty for all dates'
                          //   }
                          // }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <MobileDatePicker
                          label="End Date"
                          value={excelEndDate}
                          format="DD/MM/YYYY"
                          onChange={setExcelEndDate}
                          renderInput={(params) => <TextField {...params} fullWidth />}
                          // slotProps={{
                          //   textField: {
                          //     helperText: 'Leave empty for all dates'
                          //   }
                          // }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4} md={12}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            onClick={() => {
                              setExcelStartDate(null);
                              setExcelEndDate(null);
                            }}
                            sx={{
                              borderColor: '#ffae5f',
                              color: '#ffae5f',
                              '&:hover': {
                                borderColor: '#ff9800',
                                backgroundColor: 'rgba(255, 174, 95, 0.1)'
                              }
                            }}
                          >
                            Clear Dates
                          </Button>
                          <Button
                            variant="outlined"
                            onClick={() => {
                              const thirtyDaysAgo = dayjs().subtract(30, 'day');
                              const today = dayjs();
                              setExcelStartDate(thirtyDaysAgo);
                              setExcelEndDate(today);
                            }}
                            sx={{
                              borderColor: '#ffae5f',
                              color: '#ffae5f',
                              '&:hover': {
                                borderColor: '#ff9800',
                                backgroundColor: 'rgba(255, 174, 95, 0.1)'
                              }
                            }}
                          >
                            Last 30 Days
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>

                {/* Quick Action Reports */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                  <Grid item xs={12} sm={6} md={6}>
                    <Card
                      sx={{
                        border: '1px solid rgba(255, 174, 95, 0.2)',
                        borderRadius: 3,
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          borderColor: '#ffae5f',
                          background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                        }
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', p: 3 }}>
                        <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                          <FundOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                          Campaign Financial
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {user?.ngo_id ? 'Your campaign performance' : 'All campaigns financial summary'}
                          {(excelStartDate || excelEndDate) && (
                            <>
                              <br />
                              <strong>Filtered by date range</strong>
                            </>
                          )}
                        </Typography>
                        <Button
                          variant="contained"
                          onClick={handleCampaignFinancialReport}
                          disabled={excelLoading}
                          startIcon={<DownloadOutlined />}
                          sx={{
                            backgroundColor: '#ffae5f',
                            '&:hover': { backgroundColor: '#ff9800' }
                          }}
                        >
                          Generate
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={6} md={6}>
                    <Card
                      sx={{
                        border: '1px solid rgba(255, 174, 95, 0.2)',
                        borderRadius: 3,
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          borderColor: '#ffae5f',
                          background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                        }
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', p: 3 }}>
                        <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                          <CalendarOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                          Event Attendance
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {user?.ngo_id ? 'Your event performance' : 'All events attendance summary'}
                          {(excelStartDate || excelEndDate) && (
                            <>
                              <br />
                              <strong>Filtered by date range</strong>
                            </>
                          )}
                        </Typography>
                        <Button
                          variant="contained"
                          onClick={handleEventAttendanceReport}
                          disabled={excelLoading}
                          startIcon={<DownloadOutlined />}
                          sx={{
                            backgroundColor: '#ffae5f',
                            '&:hover': { backgroundColor: '#ff9800' }
                          }}
                        >
                          Generate
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Admin Only - Platform Revenue */}
                  {/* {!user?.ngo_id && (
                    <Grid item xs={12} sm={6} md={4}>
                      <Card
                        sx={{
                          border: '1px solid rgba(255, 174, 95, 0.2)',
                          borderRadius: 3,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            borderColor: '#ffae5f',
                            background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                          }
                        }}
                      >
                        <CardContent sx={{ textAlign: 'center', p: 3 }}>
                          <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                            <DollarOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
                          </Avatar>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                            Platform Revenue
                          </Typography>

                          <Chip label="Admin Only" size="small" color="primary" sx={{ mb: 2 }} />
                          <br />
                          <Button
                            variant="contained"
                            onClick={handlePlatformRevenueReport}
                            disabled={excelLoading}
                            startIcon={<DownloadOutlined />}
                            sx={{
                              backgroundColor: '#ffae5f',
                              '&:hover': { backgroundColor: '#ff9800' }
                            }}
                          >
                            Generate
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  )} */}

                  {/* NGO Only - Transaction Log */}
                  {user?.ngo_id && (
                    <Grid item xs={12} sm={6} md={4}>
                      <Card
                        sx={{
                          border: '1px solid rgba(255, 174, 95, 0.2)',
                          borderRadius: 3,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            borderColor: '#ffae5f',
                            background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
                          }
                        }}
                      >
                        <CardContent sx={{ textAlign: 'center', p: 3 }}>
                          <Avatar sx={{ bgcolor: 'rgba(255, 174, 95, 0.15)', width: 56, height: 56, mx: 'auto', mb: 2 }}>
                            <AuditOutlined style={{ color: '#ffae5f', fontSize: 24 }} />
                          </Avatar>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                            Transaction Log
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Detailed donation transactions
                            {(excelStartDate || excelEndDate) && (
                              <>
                                <br />
                                <strong>Filtered by date range</strong>
                              </>
                            )}
                          </Typography>
                          <Chip label="NGO Only" size="small" color="secondary" sx={{ mb: 2 }} />
                          <br />
                          <Button
                            variant="contained"
                            onClick={handleTransactionLogReport}
                            disabled={excelLoading}
                            startIcon={<DownloadOutlined />}
                            sx={{
                              backgroundColor: '#ffae5f',
                              '&:hover': { backgroundColor: '#ff9800' }
                            }}
                          >
                            Generate
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  )}
                </Grid>

                <Divider sx={{ my: 4 }} />

                {/* Audit Log Report with Filters */}
                <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#ffae5f', fontWeight: 600 }}>
                  🔍 Audit Log Report (Advanced Filters)
                </Typography>

                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Donation Type</InputLabel>
                      <Select value={donationType} label="Donation Type" onChange={(e) => setDonationType(e.target.value)}>
                        {DONATION_TYPES.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            {type.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Transaction Status</InputLabel>
                      <Select value={auditStatus} label="Transaction Status" onChange={(e) => setAuditStatus(e.target.value)}>
                        {AUDIT_STATUS_OPTIONS.map((status) => (
                          <MenuItem key={status.value} value={status.value}>
                            {status.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Donor Location</InputLabel>
                      <Select
                        value={isDonorWithinMaharashtra === null ? 'null' : isDonorWithinMaharashtra.toString()}
                        label="Donor Location"
                        onChange={(e) => setIsDonorWithinMaharashtra(e.target.value === 'null' ? null : e.target.value === 'true')}
                      >
                        {LOCATION_OPTIONS.map((option, index) => (
                          <MenuItem key={index} value={option.value === null ? 'null' : option.value.toString()}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  {user?.roleInfo?.name.startsWith('DR') && (
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControl fullWidth>
                        <InputLabel>NGO Location</InputLabel>
                        <Select
                          value={isNgoWithinMaharashtra === null ? 'null' : isNgoWithinMaharashtra.toString()}
                          label="NGO Location"
                          onChange={(e) => setIsNgoWithinMaharashtra(e.target.value === 'null' ? null : e.target.value === 'true')}
                        >
                          {LOCATION_OPTIONS.map((option, index) => (
                            <MenuItem key={index} value={option.value === null ? 'null' : option.value.toString()}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  )}
                </Grid>

                {/* Date Range for Audit Log */}
                <Grid container spacing={3} sx={{ mt: 2 }}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                      📅 Date Range (Optional)
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <MobileDatePicker
                      label="Start Date"
                      value={excelStartDate}
                      onChange={setExcelStartDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                      slotProps={{
                        textField: {
                          helperText: 'Filter transactions from this date'
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <MobileDatePicker
                      label="End Date"
                      value={excelEndDate}
                      onChange={setExcelEndDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                      slotProps={{
                        textField: {
                          helperText: 'Filter transactions until this date'
                        }
                      }}
                    />
                  </Grid>
                </Grid>

                <Box sx={{ textAlign: 'center', mb: 3, mt: 3 }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleAuditLogReport}
                    disabled={excelLoading}
                    startIcon={<AuditOutlined />}
                    sx={{
                      backgroundColor: '#ffae5f',
                      '&:hover': { backgroundColor: '#ff9800' },
                      px: 4,
                      py: 1.5
                    }}
                  >
                    {excelLoading ? <CircularProgress size={24} color="inherit" /> : 'Generate Audit Log Report'}
                  </Button>
                </Box>

                {/* Excel Reports Status Messages */}
                {excelError && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {excelError}
                  </Alert>
                )}
                {excelSuccess && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    {excelSuccess}
                  </Alert>
                )}
              </Box>
            )}

            {/* Individual Reports Tab */}
            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#ffae5f', fontWeight: 600 }}>
                  🎯 Individual Campaign & Event Reports
                </Typography>

                {/* Report Type Selection */}
                <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                      📋 Select Report Type
                    </Typography>
                    <FormControl component="fieldset" fullWidth>
                      <RadioGroup
                        row
                        value={individualReportType}
                        onChange={(e) => {
                          setIndividualReportType(e.target.value);
                          setSelectedItem(null);
                          setIndividualSearchTerm('');
                          setIndividualSearchOptions([]);
                          setIndividualError('');
                          setIndividualSuccess('');
                        }}
                      >
                        <FormControlLabel value="campaign" control={<Radio />} label="📈 Campaign Donor Report" />
                        <FormControlLabel value="event" control={<Radio />} label="👥 Event Volunteer Report" />
                      </RadioGroup>
                    </FormControl>
                  </CardContent>
                </Card>

                {/* Search Section */}
                <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                      🔍 Search & Select {individualReportType === 'campaign' ? 'Campaign' : 'Event'}
                    </Typography>

                    <Grid container spacing={3} alignItems="center">
                      <Grid item xs={12} sm={8}>
                        <TextField
                          label={`Search ${individualReportType === 'campaign' ? 'Campaigns' : 'Events'}`}
                          value={individualSearchTerm}
                          onChange={(e) => setIndividualSearchTerm(e.target.value)}
                          fullWidth
                          placeholder={`Enter ${individualReportType} name to search...`}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleIndividualSearch();
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Button
                          variant="contained"
                          onClick={handleIndividualSearch}
                          disabled={individualSearchLoading || !individualSearchTerm.trim()}
                          fullWidth
                          sx={{
                            backgroundColor: '#ffae5f',
                            '&:hover': { backgroundColor: '#ff9800' }
                          }}
                        >
                          {individualSearchLoading ? <CircularProgress size={20} color="inherit" /> : 'Search'}
                        </Button>
                      </Grid>
                    </Grid>

                    {/* Search Results */}
                    {individualSearchOptions.length > 0 && (
                      <Box sx={{ mt: 3 }}>
                        <Autocomplete
                          options={individualSearchOptions}
                          getOptionLabel={(option) => option.label}
                          value={selectedItem}
                          onChange={(_, value) => setSelectedItem(value)}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label={`Select ${individualReportType === 'campaign' ? 'Campaign' : 'Event'}`}
                              placeholder="Choose from search results..."
                            />
                          )}
                          renderOption={(props, option) => (
                            <Box component="li" {...props}>
                              <Box>
                                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                  {option.label}
                                </Typography>
                                {option.value?.description && (
                                  <Typography variant="body2" color="text.secondary">
                                    {option.value.description}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          )}
                        />
                      </Box>
                    )}
                  </CardContent>
                </Card>

                {/* Date Range Section */}
                {selectedItem && (
                  <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                        📅 Date Range Filter (Optional)
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        {individualReportType === 'campaign' ? 'Filter donations by transaction date' : 'Filter volunteers by RSVP date'}
                      </Typography>

                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <MobileDatePicker
                            label="Start Date"
                            value={individualStartDate}
                            onChange={setIndividualStartDate}
                            renderInput={(params) => <TextField {...params} fullWidth />}
                            slotProps={{
                              textField: {
                                helperText: 'Leave empty for all dates'
                              }
                            }}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <MobileDatePicker
                            label="End Date"
                            value={individualEndDate}
                            onChange={setIndividualEndDate}
                            renderInput={(params) => <TextField {...params} fullWidth />}
                            slotProps={{
                              textField: {
                                helperText: 'Leave empty for all dates'
                              }
                            }}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                )}

                {/* Generate Report Section */}
                {selectedItem && (
                  <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        📊 Generate {individualReportType === 'campaign' ? 'Donor' : 'Volunteer'} Report
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 3 }}>
                        Selected: <strong>{selectedItem.label}</strong>
                      </Typography>

                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleIndividualReportGenerate}
                        disabled={individualLoading}
                        startIcon={individualReportType === 'campaign' ? <FundOutlined /> : <CalendarOutlined />}
                        sx={{
                          backgroundColor: '#ffae5f',
                          '&:hover': { backgroundColor: '#ff9800' },
                          px: 4,
                          py: 1.5
                        }}
                      >
                        {individualLoading ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          `Generate ${individualReportType === 'campaign' ? 'Donor' : 'Volunteer'} Report`
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                )}

                {/* Individual Reports Status Messages */}
                {individualError && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {individualError}
                  </Alert>
                )}
                {individualSuccess && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    {individualSuccess}
                  </Alert>
                )}
              </Box>
            )}

            {/* Legacy Reports Tab */}
            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom sx={{ mb: 3, color: '#ffae5f', fontWeight: 600 }}>
                  📋 Legacy Report System
                </Typography>

                <FormControl component="fieldset" fullWidth margin="normal">
                  <FormLabel component="legend">Report Type</FormLabel>
                  <RadioGroup row value={reportType} onChange={handleReportTypeChange} name="report-type">
                    {REPORT_TYPES.map((type) => (
                      <FormControlLabel key={type.value} value={type.value} control={<Radio />} label={type.label} />
                    ))}
                  </RadioGroup>
                </FormControl>

                <Grid container spacing={2} alignItems="center" mt={1}>
                  <Grid item xs={8}>
                    <TextField
                      label={`Search ${REPORT_TYPES.find((t) => t.value === reportType).label}`}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      fullWidth
                      disabled={
                        (reportType === 'ngos' && user?.ngo_id) ||
                        (reportType === 'causes' && user?.ngo_id) ||
                        reportType === 'causes' ||
                        reportType === 'buckets'
                      }
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSearch();
                      }}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <Button
                      variant="contained"
                      onClick={handleSearch}
                      disabled={
                        searchLoading ||
                        (reportType === 'ngos' && user?.ngo_id) ||
                        (reportType === 'causes' && user?.ngo_id) ||
                        reportType === 'causes' ||
                        reportType === 'buckets'
                      }
                      fullWidth
                    >
                      {searchLoading ? <CircularProgress size={20} /> : 'Search'}
                    </Button>
                  </Grid>
                  <Grid item xs={12}>
                    <Autocomplete
                      multiple
                      options={options}
                      getOptionLabel={(option) => option.label}
                      value={options.filter((opt) => selectedIds.includes(opt.id))}
                      onChange={(_, value) => setSelectedIds(value.map((v) => v.id))}
                      renderInput={(params) => (
                        <TextField {...params} label={`Select ${REPORT_TYPES.find((t) => t.value === reportType).label}`} />
                      )}
                      disabled={
                        (reportType === 'ngos' && user?.ngo_id) ||
                        (reportType === 'causes' && user?.ngo_id) ||
                        options.length === 0 ||
                        searchLoading
                      }
                      loading={searchLoading}
                    />
                  </Grid>
                </Grid>

                {/* Date pickers and status dropdown */}
                <Grid container spacing={2} mt={2} alignItems="center">
                  <Grid item xs={12} sm={4}>
                    <MobileDatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={setStartDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <MobileDatePicker
                      label="End Date"
                      value={endDate}
                      onChange={setEndDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select value={status} label="Status" onChange={(e) => setStatus(e.target.value)}>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.value} value={opt.value}>
                            {opt.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                {/* Legacy Reports Status Messages */}
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                {success && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    {success}
                  </Alert>
                )}

                <Box mt={3}>
                  <Button variant="contained" color="primary" onClick={handleGenerateReport} disabled={generating} fullWidth>
                    {generating ? <CircularProgress size={24} color="inherit" /> : 'Generate Report'}
                  </Button>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
}
