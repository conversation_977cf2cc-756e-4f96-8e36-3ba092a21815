import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Grid,
  Typography,
  CircularProgress,
  Button,
  Chip,
  LinearProgress,
  Tooltip,
  Avatar,
  Select,
  MenuItem,
  FormControl
} from '@mui/material';
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart';
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/BarChart';

import { CalendarOutlined, ClockCircleOutlined, HeartOutlined, TeamOutlined, TrophyOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';

// Services
import { getNgoEventStats } from 'pages/masters/apis/event-stats.service';

// Components
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';
import { formatAmountIndian } from 'utils/permissionUtils';

const COLORS = ['#ffae5f', '#ff9800', '#f57c00', '#e65100', '#bf360c', '#8d6e63'];
const GENDER_COLORS_EXTENDED = [
  '#6FB1FC',
  '#FFB6C1', // Female - Light Pink
  '#C0C0C0', // Other - Neutral Gray
  '#B39DDB', // Transgender / Genderqueer - Lavender
  '#AED581', // Prefer not to say - Soft Green
  '#FFF176' // Intersex or Custom - Soft Yellow
];

const EventsStatistics = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Detail view states
  const [showDetailView, setShowDetailView] = useState(false);
  const [detailViewType, setDetailViewType] = useState(''); // 'rsvps', 'volunteers'
  const [detailViewData, setDetailViewData] = useState([]);
  const [detailViewTitle, setDetailViewTitle] = useState('');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [selectedVolunteer, setSelectedVolunteer] = useState(null);

  const user = JSON.parse(localStorage.getItem('user'));
  const ngoId = user?.ngo_id || null;

  useEffect(() => {
    fetchEventStats();
  }, [ngoId]);

  const fetchEventStats = async () => {
    try {
      setLoading(true);
      const response = await getNgoEventStats(ngoId);

      if (response?.success) {
        setStats(response.data);
      } else {
        toast.error('Failed to fetch event statistics');
      }
    } catch (error) {
      console.error('Error fetching event stats:', error);
      toast.error('An error occurred while fetching event statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (eventId) => {
    navigate(`/dashboard/event-report/${eventId}`);
  };

  // Handle clicking on RSVP count to show RSVP details
  const handleEventRsvpClick = (event) => {
    if (event.rsvpDetails && event.rsvpDetails.length > 0) {
      setSelectedEvent(event);
      setDetailViewData(event.rsvpDetails);
      setDetailViewType('rsvps');
      setDetailViewTitle(`${event.eventName} - RSVP Details`);
      setShowDetailView(true);
    }
  };

  // Handle clicking on volunteer to show participation details
  const handleVolunteerClick = (volunteer) => {
    if (volunteer.participationDetails && volunteer.participationDetails.length > 0) {
      setSelectedVolunteer(volunteer);
      setDetailViewData(volunteer.participationDetails);
      setDetailViewType('volunteers');
      setDetailViewTitle(`${volunteer.volunteerName}'s Participation History`);
      setShowDetailView(true);
    }
  };

  // Handle back button from detail view
  const handleBackToMain = () => {
    setShowDetailView(false);
    setDetailViewType('');
    setDetailViewData([]);
    setDetailViewTitle('');
    setSelectedEvent(null);
    setSelectedVolunteer(null);
  };

  // Get columns for RSVP details table
  const getRsvpDetailsColumns = () => [
    {
      accessorKey: 'volunteerName',
      header: 'Volunteer',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600}>
          {cell.row.original.volunteerName}
        </Typography>
      )
    },
    {
      accessorKey: 'volunteerEmail',
      header: 'Email',
      cell: ({ cell }) => (
        <Typography variant="body2" color="text.secondary">
          {cell.row.original.volunteerEmail}
        </Typography>
      )
    },
    {
      accessorKey: 'rsvpValue',
      header: 'RSVP',
      cell: ({ cell }) => (
        <Chip
          label={cell.row.original.rsvpValue?.toUpperCase()}
          size="small"
          color={cell.row.original.rsvpValue === 'yes' ? 'success' : 'error'}
          variant="outlined"
        />
      )
    },
    {
      accessorKey: 'type',
      header: 'Status',
      cell: ({ cell }) => (
        <Chip
          label={cell.row.original.type}
          size="small"
          color={cell.row.original.type === 'Scanned' ? 'primary' : 'default'}
          variant="filled"
        />
      )
    },
    {
      accessorKey: 'actualHours',
      header: 'Hours',
      cell: ({ cell }) => <Typography variant="body2">{cell.row.original.actualHours || 0}h</Typography>
    },
    {
      accessorKey: 'rsvpDate',
      header: 'RSVP Date',
      cell: ({ cell }) => <Typography variant="body2">{dayjs(cell.row.original.rsvpDate).format('DD/MM/YYYY')}</Typography>
    }
  ];

  // Get columns for volunteer participation details table
  const getVolunteerDetailsColumns = () => [
    {
      accessorKey: 'eventName',
      header: 'Event',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600}>
          {cell.row.original.eventName}
        </Typography>
      )
    },
    {
      accessorKey: 'eventDate',
      header: 'Event Date',
      cell: ({ cell }) => <Typography variant="body2">{dayjs(cell.row.original.eventDate).format('DD/MM/YYYY')}</Typography>
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ cell }) => (
        <Typography variant="body2" color="text.secondary">
          {cell.row.original.location}
        </Typography>
      )
    },
    {
      accessorKey: 'actualHours',
      header: 'Actual Hours',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600}>
          {cell.row.original.actualHours || 0}h
        </Typography>
      )
    },
    {
      accessorKey: 'impactHours',
      header: 'Impact Hours',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600} color="primary.main">
          {cell.row.original.impactHours || 0}h
        </Typography>
      )
    },
    {
      accessorKey: 'participationDate',
      header: 'Participation Date',
      cell: ({ cell }) => <Typography variant="body2">{dayjs(cell.row.original.participationDate).format('DD/MM/YYYY')}</Typography>
    }
  ];

  const formatHours = (hours) => {
    if (!hours) return '0 hrs';
    return `${hours.toLocaleString('en-IN')} hrs`;
  };

  const formatLives = (hours) => {
    if (!hours) return '0 lives';
    return `${hours.toLocaleString('en-IN')} lives`;
  };

  const getEventTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'physical':
        return 'primary';
      case 'online':
        return 'secondary';
      default:
        return 'default';
    }
  };

  // Show detail view if active
  if (showDetailView) {
    return (
      <Box sx={{ p: 3 }}>
        {/* Back Button */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            onClick={handleBackToMain}
            sx={{
              borderColor: '#ffae5f',
              color: '#ffae5f',
              '&:hover': {
                borderColor: '#ff9800',
                backgroundColor: 'rgba(255, 174, 95, 0.1)'
              }
            }}
          >
            ← Back to Dashboard
          </Button>
        </Box>

        {/* Detail View Header */}
        <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
          {detailViewTitle}
        </Typography>

        {/* Selected Event Summary */}
        {selectedEvent && (
          <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {selectedEvent.rsvpStatistics?.rsvpYesCount || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      RSVP Yes
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {selectedEvent.rsvpStatistics?.actualParticipants || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Attended
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {selectedEvent.rsvpStatistics?.conversionRate || 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Conversion Rate
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {dayjs(selectedEvent.eventDate).format('DD/MM/YYYY')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Event Date
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Selected Volunteer Summary */}
        {selectedVolunteer && (
          <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {selectedVolunteer.totalVolunteerHours || 0}h
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Hours
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {selectedVolunteer.eventsParticipated || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Events Participated
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {selectedVolunteer.email}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Email Address
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {dayjs(selectedVolunteer.lastParticipationDate).format('DD/MM/YYYY')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last Participation
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Detail Data Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {detailViewType === 'rsvps' ? 'RSVP Details' : 'Participation Details'} ({detailViewData.length} records)
            </Typography>
            <CustomerTableWithoutFilter
              data={detailViewData}
              columns={detailViewType === 'rsvps' ? getRsvpDetailsColumns() : getVolunteerDetailsColumns()}
              category={detailViewType === 'rsvps' ? 'RSVP Details' : 'Participation Details'}
            />
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
        <CircularProgress />
        <Typography sx={{ margin: 2 }}>Loading Event Statistics...</Typography>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary">
          No event statistics available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
        Event Statistics Dashboard
      </Typography>

      {/* KPI Cards */}
      {/* <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <ClockCircleOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {formatHours(stats.totalVolunteerHours)}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Volunteer Hours
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Across all events
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <HeartOutlined style={{ color: '#ffae5f', fontSize: 22 }} />{' '}
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {formatLives(stats.totalImpactHours)}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Impacted Lives
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Actual lives impacted
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <TeamOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {stats.rsvpStatistics?.actualParticipants?.toLocaleString() || 0}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Participants
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {stats.rsvpStatistics?.conversionRate?.toFixed(1)}% conversion rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <CalendarOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {stats.conversionFunnel?.totalEvents || 0}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Events
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Events organized
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid> */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Volunteer Hours */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <ClockCircleOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Volunteer Hours
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Across all events
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {formatHours(stats.totalVolunteerHours)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Impacted Lives */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <HeartOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Impacted Lives
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Lives actually impacted
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {formatLives(stats.totalImpactHours)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Participants */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <TeamOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Participants
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {stats.rsvpStatistics?.conversionRate?.toFixed(1)}% conversion rate
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {stats.rsvpStatistics?.actualParticipants?.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Events */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <CalendarOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Events
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Events organized
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {stats.conversionFunnel?.totalEvents || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Gender Demographics - Pie Chart */}
        <Grid item xs={12} md={6}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                Gender Demographics
              </Typography>
              {stats.demographicData && stats.demographicData.length > 0 ? (
                <PieChart
                  series={[
                    {
                      data: stats.demographicData.map((item, index) => ({
                        id: index,
                        value: item.count,
                        label: item.gender,
                        color: GENDER_COLORS_EXTENDED[index % GENDER_COLORS_EXTENDED.length]
                      }))
                    }
                  ]}
                  width={400}
                  height={300}
                  slotProps={{
                    legend: {
                      direction: 'column',
                      position: { vertical: 'top', horizontal: 'right' }
                    }
                  }}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No demographic data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* RSVP vs Participation - Bar Chart */}
        <Grid item xs={12} md={6}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                RSVP vs Participation
              </Typography>
              {stats.rsvpStatistics ? (
                <BarChart
                  xAxis={[
                    {
                      scaleType: 'band',
                      data: ['RSVP Yes', 'RSVP No', 'Participated', 'No Show']
                    }
                  ]}
                  series={[
                    {
                      data: [
                        stats.rsvpStatistics.rsvpYesCount,
                        stats.rsvpStatistics.rsvpNoCount,
                        stats.rsvpStatistics.actualParticipants,
                        stats.rsvpStatistics.acknowledgedButNoShow
                      ],
                      label: 'Count',
                      color: '#ffae5f'
                    }
                  ]}
                  width={500}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No RSVP data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Volunteers Table */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                Top Volunteers
              </Typography>
              {stats.topVolunteers && stats.topVolunteers.length > 0 ? (
                <CustomerTableWithoutFilter
                  data={stats.topVolunteers}
                  columns={[
                    {
                      accessorKey: 'volunteerName',
                      header: 'Volunteer Name',
                      cell: ({ cell }) => (
                        <Tooltip title={cell.row.original.volunteerName} arrow>
                          <Typography
                            variant="body2"
                            fontWeight={600}
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {cell.row.original.volunteerName}
                          </Typography>
                        </Tooltip>
                      )
                    },
                    {
                      accessorKey: 'email',
                      header: 'Email',
                      cell: ({ cell }) => (
                        <Typography variant="body2" color="text.secondary">
                          {cell.row.original.email}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'totalVolunteerHours',
                      header: 'Volunteer Hours',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600} sx={{ color: '#ffae5f' }}>
                          {formatHours(cell.row.original.totalVolunteerHours)}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'eventsParticipated',
                      header: 'Events',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.eventsParticipated}
                          size="small"
                          onClick={() => handleVolunteerClick(cell.row.original)}
                          sx={{
                            backgroundColor: 'rgba(255, 174, 95, 0.1)',
                            color: '#ffae5f',
                            fontWeight: 600,
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 174, 95, 0.2)',
                              transform: 'scale(1.05)'
                            }
                          }}
                        />
                      )
                    },
                    {
                      accessorKey: 'lastParticipationDate',
                      header: 'Last Participation',
                      cell: ({ cell }) => (
                        <Typography variant="body2">{dayjs(cell.row.original.lastParticipationDate).format('DD/MM/YYYY')}</Typography>
                      )
                    }
                  ]}
                  category="Top Volunteers"
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No volunteer data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Events Performance Table */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card sx={{ border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#ffae5f' }}>
                Top Events Performance
              </Typography>
              {stats.topEvents && stats.topEvents.length > 0 ? (
                <CustomerTableWithoutFilter
                  data={stats.topEvents}
                  columns={[
                    {
                      accessorKey: 'eventName',
                      header: 'Event Name',
                      cell: ({ cell }) => (
                        <Box>
                          <Tooltip title={cell.row.original.eventName} arrow placement="top-start">
                            <Typography
                              variant="body2"
                              fontWeight={600}
                              onClick={() => handleViewReport(cell.row.original.id)}
                              sx={{
                                cursor: 'pointer',
                                color: '#ffae5f',
                                fontWeight: 700,
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                '&:hover': {
                                  textDecoration: 'underline'
                                }
                              }}
                            >
                              {cell.row.original.eventName}
                            </Typography>
                          </Tooltip>
                          <Tooltip title={cell.row.original.location} arrow placement="bottom-end">
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}
                            >
                              {cell.row.original.location}
                            </Typography>
                          </Tooltip>
                        </Box>
                      )
                    },
                    {
                      accessorKey: 'eventType',
                      header: 'Type',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.eventType?.charAt(0).toUpperCase() + cell.row.original.eventType?.slice(1).toLowerCase()}
                          size="small"
                          color={getEventTypeColor(cell.row.original.eventType)}
                          variant="filled"
                        />
                      )
                    },
                    {
                      accessorKey: 'eventDate',
                      header: 'Date',
                      cell: ({ cell }) => <Typography variant="body2">{dayjs(cell.row.original.eventDate).format('DD/MM/YYYY')}</Typography>
                    },
                    {
                      accessorKey: 'rsvpYesCount',
                      header: 'RSVPs',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.rsvpYesCount}
                          size="small"
                          color="primary"
                          variant="outlined"
                          onClick={() => handleEventRsvpClick(cell.row.original)}
                          sx={{
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 174, 95, 0.1)',
                              borderColor: '#ffae5f'
                            }
                          }}
                        />
                      )
                    },
                    {
                      accessorKey: 'scannedCount',
                      header: 'Participants',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.scannedCount}
                          size="small"
                          color="primary"
                          variant="outlined"
                          onClick={() => handleEventRsvpClick(cell.row.original)}
                          sx={{
                            cursor: 'pointer',
                            color: '#ffae5f',
                            borderColor: '#ffae5f',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 174, 95, 0.1)',
                              borderColor: '#ffae5f'
                            }
                          }}
                        />
                      )
                    },
                    {
                      accessorKey: 'conversionRate',
                      header: 'Conversion Rate',
                      cell: ({ cell }) => {
                        const rate = parseFloat(cell.row.original.conversionRate) || 0;
                        return (
                          <Box sx={{ width: '100%' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Typography variant="body2" sx={{ minWidth: 35 }}>
                                {rate.toFixed(1)}%
                              </Typography>
                            </Box>
                            <LinearProgress variant="determinate" value={Math.min(rate, 100)} sx={{ height: 6, borderRadius: 3 }} />
                          </Box>
                        );
                      }
                    },
                    {
                      accessorKey: 'totalImpactHours',
                      header: 'Lives Impacted',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600} sx={{ color: '#ffae5f' }}>
                          {formatLives(cell.row.original.totalImpactHours)}
                        </Typography>
                      )
                    }
                  ]}
                  category="Top Events Performance"
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No event data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EventsStatistics;
