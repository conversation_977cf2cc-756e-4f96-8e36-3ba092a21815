import React, { useEffect, useState } from 'react';
import axios from 'axios';

// material-ui
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';

// project imports
import MainCard from 'components/MainCard';
import {
  addCampaignImageService,
  addNGOImageService,
  addProductImageService,
  deleteCampaignImageService,
  deleteNGOImageService,
  deleteProductImageService,
  getCampaignImages,
  getNGOImages,
  getProductImages,
  updateCampaignImageService,
  updateNGOImageService,
  updateProductImageService,
  getCategoryImages,
  addCategoryImageService,
  deleteCategoryImageService,
  updateCategoryImageService
} from './images.service';
import * as yup from 'yup';
import { Button, DialogActions, Dialog, DialogTitle, DialogContent, TextField, FormHelperText, Stack } from '@mui/material';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import useAuth from 'hooks/useAuth';
import { Formik } from 'formik';
import { getConvertedFileName } from 'utils/permissionUtils';
import { BASE_URL, IMAGE_BASE_URL } from 'sections/apps/profiles/profile.service';
import { useParams } from 'react-router';

// material-ui styles
const mediaSX = {
  width: 120,
  height: 100,
  borderRadius: 1
};

const typeMap = {
  ngo: 'NGO',
  campaign: 'Campaign',
  product: 'Product',
  category: 'Category'
};

export default function ImageComponent({ type, id }) {
  const { user } = useAuth();
  const { campaignId, newNgoId, categoryId } = useParams();
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentImage, setCurrentImage] = useState(null);
  const [fetchImageApiName, SetFetchImageApiName] = useState(null);

  const ngoId = user?.ngo_id ? user?.ngo_id : newNgoId;

  useEffect(() => {
    fetchImages();
  }, [type, campaignId, ngoId]);

  const fetchImages = async () => {
    try {
      let response;
      if (type === 'ngo') {
        response = await getNGOImages(ngoId);
        SetFetchImageApiName('fetchMultipleNgoImages');
      } else if (type === 'campaign') {
        response = await getCampaignImages(campaignId);
        SetFetchImageApiName('fetchMultipleCampaignImages');
      } else if (type === 'product') {
        response = await getProductImages(id); // For products
        SetFetchImageApiName('fetchProductImages');
      } else if (type === 'category') {
        response = await getCategoryImages(categoryId);
        SetFetchImageApiName('fetchCategoryImages');
      }
      setImages(response);
      setLoading(false);
    } catch (err) {
      setError(err.message || 'Something went wrong while fetching data.');
      setLoading(false);
    }
  };

  const handleAddOrEdit = async (values) => {
    if (!isEditing && type === 'category' && images?.length >= 5) {
      toast.warning('You cannot add more than 5 images for a Category.');
      return;
    }

    try {
      const formData = new FormData();

      if (values?.files && values?.files?.length > 0) {
        delete values.image_url;
        const convertedFileName = getConvertedFileName(values.files[0].name);
        formData.append('fileName', `${convertedFileName}`);
        formData.append('file', values?.files[0], `${convertedFileName}`);
      }
      if (campaignId) formData.append('campaign_id', campaignId);
      else if (ngoId) formData.append('ngo_id', ngoId);
      if (type === 'product') formData.append('product_id', id);
      if (type === 'category') formData.append('category_id', categoryId);
      Object.keys(values).forEach((key) => {
        formData.append(key, values[key]);
      });

      let response;
      if (isEditing) {
        if (type === 'ngo') {
          response = await updateNGOImageService(currentImage.id, formData);
        } else if (type === 'campaign') {
          response = await updateCampaignImageService(currentImage.id, formData);
        } else if (type === 'product') {
          response = await updateProductImageService(currentImage.id, formData);
        } else if (type === 'category') {
          response = await updateCategoryImageService(currentImage.id, formData);
        }

        if (response.status === 200) {
          toast.success(`${typeMap[type] || 'The'} image updated successfully!`);
          setOpenDialog(false);
          fetchImages();
        }
      } else {
        if (type === 'ngo') {
          response = await addNGOImageService(formData);
        } else if (type === 'campaign') {
          response = await addCampaignImageService(formData);
        } else if (type === 'product') {
          response = await addProductImageService(formData);
        } else if (type === 'category') {
          response = await addCategoryImageService(formData);
        }

        if (response.status) {
          toast.success(`${typeMap[type] || 'The'} image added successfully!`);
          setOpenDialog(false);
          fetchImages();
        } else {
          toast.error(`Failed to add ${typeMap[type] || ''} image`);
        }
      }
    } catch (error) {
      console.error(`Failed to save ${typeMap[type] || 'the'} image:`, error);
      toast.error(`Failed to save ${typeMap[type] || 'the'} image`);
    }
  };

  const openEditDialog = (image) => {
    setCurrentImage(image);
    setIsEditing(true);
    setOpenDialog(true);
  };

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this Image?');
    if (confirmDelete) {
      try {
        let response;
        if (type === 'ngo') {
          response = await deleteNGOImageService(id);
        } else if (type === 'campaign') {
          response = await deleteCampaignImageService(id);
        } else if (type === 'product') {
          response = await deleteProductImageService(id);
        } else if (type === 'category') {
          response = await deleteCategoryImageService(id);
        }
        toast.success('Image deleted successfully!');
        fetchImages();
      } catch (error) {
        console.error('Failed to delete Image:', error);
        toast.error('Failed to delete image');
      }
    }
  };

  // Add a function to check if this is the gallery-images route
  const isGalleryImages = type === 'ngo' && window.location.pathname.includes('gallery-images');

  const openAddDialog = () => {
    if (isGalleryImages && images.length >= 10) {
      toast.error('You can add a maximum of 10 gallery images.');
      return;
    }
    if (type === 'category' && images?.length >= 5) {
      toast.warning('You can only upload up to 5 images for a Category.');
      return;
    }
    setCurrentImage(null);
    setIsEditing(false);
    setOpenDialog(true);
  };

  if (loading) {
    return (
      <MainCard title="Images">
        <CardContent>
          <Grid container justifyContent="center">
            <CircularProgress />
          </Grid>
        </CardContent>
      </MainCard>
    );
  }

  if (error) {
    return (
      <MainCard title="Images">
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </MainCard>
    );
  }

  return (
    <>
      <Grid container justifyContent="flex-end" sx={{ marginBottom: 2 }}>
        <Button variant="contained" sx={{ textDecoration: 'none', textTransform: 'none' }} color="primary" onClick={openAddDialog}>
          Add an image
        </Button>
      </Grid>
      <MainCard content={false}>
        <CardContent>
          {images?.length > 0 ? (
            <Grid container spacing={3}>
              {images.map((image, index) => (
                <Grid item xs={12} key={index}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item>
                      <CardMedia
                        component="img"
                        image={`${BASE_URL}/${fetchImageApiName}/${image?.fileName}`}
                        alt={image.alt_text}
                        title={image.alt_text}
                        sx={mediaSX}
                      />
                    </Grid>
                    <Grid item xs zeroMinWidth>
                      <Grid container spacing={1}>
                        <Grid item xs={12}>
                          <Typography variant="subtitle1">{image.alt_text}</Typography>
                          <Typography color="secondary">Description: {image.description}</Typography>
                        </Grid>
                        <Grid item xs={12} container justifyContent="flex-end">
                          <Button onClick={() => handleDelete(image?.id)}>
                            <DeleteOutlined />
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Typography align="center" color="primary" sx={{ fontWeight: 'bold' }}>
              No Images Have Been Added Yet. Click the 'Add an image' button to get started.
            </Typography>
          )}
        </CardContent>
      </MainCard>

      {/* Dialog for Add/Edit Banner Image */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} PaperProps={{ sx: { borderRadius: '16px' } }}>
        <Formik
          initialValues={{ ...currentImage }}
          onSubmit={async (values) => await handleAddOrEdit(values)}
          validationSchema={yup.object().shape({
            description: yup.string().required('Description is required')
          })}
        >
          {({ values, handleSubmit, handleChange, setFieldValue, setFieldError, touched, errors }) => (
            <form onSubmit={handleSubmit}>
              <DialogTitle>{isEditing ? 'Edit Image' : 'Add Image'}</DialogTitle>
              <DialogContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sx={{ marginTop: 1 }}>
                    <TextField
                      label="Description"
                      name="description"
                      fullWidth
                      variant="outlined"
                      value={values?.description || ''}
                      onChange={handleChange}
                      required
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <UploadSingleFile
                        setFieldValue={setFieldValue}
                        setFieldError={setFieldError}
                        file={values.files}
                        error={touched.files && !!errors.files}
                        checkFileType={type === 'category'}
                        allowedTypes={type === 'category' ? ['png', 'jpg', 'jpeg'] : []}
                      />
                    </Stack>
                    {touched.files && errors.files && (
                      <FormHelperText error id="standard-weight-helper-text-password-login">
                        {errors.files}
                      </FormHelperText>
                    )}
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button type="submit" color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button>
              </DialogActions>
            </form>
          )}
        </Formik>
      </Dialog>

      <ToastContainer />
    </>
  );
}
