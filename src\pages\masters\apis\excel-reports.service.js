import { useState } from 'react';
import axiosServices from 'utils/axios';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;
const DONWLOAD_API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}`;

/**
 * Generate Campaign Financial Summary Report
 * @param {number|null} ngoId - Optional NGO ID for NGO-specific reports
 * @param {string|null} startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateCampaignFinancialReport = async (ngoId = null, startDate = null, endDate = null) => {
  try {
    const params = new URLSearchParams({ reportType: 'campaign_financial_summary' });
    if (ngoId) {
      params.append('ngoId', ngoId);
    }
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }

    const response = await axiosServices.get(`${API_BASE_URL}/reports/campaigns?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error generating campaign financial report:', error);
    throw error;
  }
};

/**
 * Generate Event Attendance Summary Report
 * @param {number|null} ngoId - Optional NGO ID for NGO-specific reports
 * @param {string|null} startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateEventAttendanceReport = async (ngoId = null, startDate = null, endDate = null) => {
  try {
    const params = new URLSearchParams({ reportType: 'event_attendance_summary' });
    if (ngoId) {
      params.append('ngoId', ngoId);
    }
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }

    const response = await axiosServices.get(`${API_BASE_URL}/reports/events?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error generating event attendance report:', error);
    throw error;
  }
};

/**
 * Generate Platform Revenue Report (Admin Only)
 * @returns {Promise} API response with download URL
 */
export const generatePlatformRevenueReport = async () => {
  try {
    const response = await axiosServices.get(`${API_BASE_URL}/reports/platform-revenue`);
    return response.data;
  } catch (error) {
    console.error('Error generating platform revenue report:', error);
    throw error;
  }
};

/**
 * Generate Transaction Log Report (NGO Only)
 * @param {number} ngoId - Required NGO ID
 * @param {string|null} startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateTransactionLogReport = async (ngoId, startDate = null, endDate = null) => {
  try {
    const params = new URLSearchParams({ ngoId: ngoId.toString() });
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }
    const response = await axiosServices.get(`${API_BASE_URL}/reports/transaction-log?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error generating transaction log report:', error);
    throw error;
  }
};

/**
 * Generate Audit Log Report
 * @param {Object} filters - Filter options
 * @param {string} filters.donationType - 'all', 'ngo', 'campaign', 'genericViaMoney'
 * @param {string} filters.status - 'all', 'captured', 'failed'
 * @param {number|null} filters.ngoId - Optional NGO ID for NGO-specific reports
 * @param {boolean|null} filters.isDonorWithinMaharashtra - Optional donor location filter
 * @param {boolean|null} filters.isNgoWithinMaharashtra - Optional NGO location filter
 * @param {string|null} filters.startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} filters.endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateAuditLogReport = async (filters = {}) => {
  try {
    const params = new URLSearchParams();

    // Add filters to params
    if (filters.donationType && filters.donationType !== 'all') {
      params.append('donationType', filters.donationType);
    }
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.ngoId) {
      params.append('ngoId', filters.ngoId.toString());
    }
    if (filters.isDonorWithinMaharashtra !== null && filters.isDonorWithinMaharashtra !== undefined) {
      params.append('isDonorWithinMaharashtra', filters.isDonorWithinMaharashtra.toString());
    }
    if (filters.isNgoWithinMaharashtra !== null && filters.isNgoWithinMaharashtra !== undefined) {
      params.append('isNgoWithinMaharashtra', filters.isNgoWithinMaharashtra.toString());
    }
    if (filters.startDate) {
      params.append('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params.append('endDate', filters.endDate);
    }

    const response = await axiosServices.get(`${API_BASE_URL}/reports/audit-log?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error generating audit log report:', error);
    throw error;
  }
};

/**
 * Download file from URL
 * @param {string} downloadUrl - The download URL from API response
 * @param {string} fileName - The file name from API response
 */
export const downloadFile = (downloadUrl, fileName) => {
  try {
    const link = document.createElement('a');
    link.href = `${DONWLOAD_API_BASE_URL}${downloadUrl}`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

/**
 * Custom hook for Excel report generation
 */
export const useExcelReports = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateAndDownload = async (reportFunction, ...args) => {
    setLoading(true);
    setError(null);

    try {
      const response = await reportFunction(...args);

      if (response?.success && response?.data?.downloadUrl) {
        downloadFile(response.data.downloadUrl, response.data.fileName);
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to generate report');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { generateAndDownload, loading, error };
};
