import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import BackLeft from './UserProfileBackLeft';
import BackRight from './UserProfileBackRight';
import MainCard from 'components/MainCard';
import ProfileRadialChart from './ProfileRadialChart';
import { ThemeDirection } from 'config';
import { Chip } from '@mui/material';
import { updateUsingPatchNGO } from '../profile.service';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import useAuth from 'hooks/useAuth';
import { SUBMIT_ONBOARDING_STATUS } from 'utils/statusconstans';
// ==============================|| USER PROFILE - TOP CARD ||============================== //

export default function ProfileCard({ focusInput, ngoInfo, profileCompletePercentage }) {
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  const handleSubmitForReview = async (e) => {
    if (profileCompletePercentage < 100) {
      toast.error(
        'Your profile is currently not 100% complete. Please update it to ensure all required information is accurately provided.'
      );
      return;
    }
    if (ngoInfo?.ngo_status == 'In Review') {
      toast.error('Your profile is already in the Review Process');
      return;
    }
    e.preventDefault();
    if (window.confirm('Are you sure you want to submit profile for review')) {
      try {
        await updateUsingPatchNGO(user?.ngo_id, { ngo_status: 'In Review', last_status: 'In Review' }, 'NGOProfile');
        toast.success('Your profile has been submitted for review.');
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } catch (error) {
        console.error('Error updating ngo details:', error);
        toast.error('Failed to update NGO details');
      }
    }
  };

  return (
    <MainCard border={false} content={false} sx={{ bgcolor: 'primary.lighter', position: 'relative' }}>
      <Box
        sx={{
          position: 'absolute',
          bottom: -7,
          left: 0,
          zIndex: 1,
          ...(theme.direction === ThemeDirection.RTL && { transform: 'rotate(180deg)', top: -7, bottom: 'unset' })
        }}
      >
        <BackLeft />
      </Box>
      <Grid container justifyContent="space-between" alignItems="center" sx={{ position: 'relative', zIndex: 5 }}>
        <Grid item>
          <Stack direction="row" spacing={{ xs: 1, sm: 2 }} alignItems="center">
            <Box sx={{ ml: { xs: 0, sm: 1 } }}>
              <ProfileRadialChart />
            </Box>
            <Stack spacing={0.75}>
              <Typography variant="h5">Your NGO profile verification status &nbsp;-&nbsp; {ngoInfo?.ngo_status}</Typography>
              {/* {ngoInfo && (
              <Typography variant="secondary" color="secondary">
                Verification Status - { ngoInfo?.ngo_status}
              </Typography>
              )} */}
            </Stack>
          </Stack>
        </Grid>
        <Grid item sx={{ mx: { xs: 2, sm: 3 }, my: { xs: 1, sm: 0 }, mb: { xs: 2, sm: 0 } }} xs={downSM ? 12 : 'auto'}>
          <div style={{ marginBottom: '4px' }}>
            <Button variant="contained" fullWidth={true} component={Link} to="/apps/profiles/account/ngoprofile" onClick={focusInput}>
              Update NGO Profile
            </Button>
          </div>
          {SUBMIT_ONBOARDING_STATUS.includes(ngoInfo?.ngo_status) && (
            <div>
              <Button variant="contained" fullWidth={true} onClick={handleSubmitForReview}>
                Submit for Onboarding
              </Button>
            </div>
          )}
        </Grid>
      </Grid>
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          zIndex: 1,
          ...(theme.direction === ThemeDirection.RTL && { transform: 'rotate(180deg)', top: -10, bottom: 'unset' })
        }}
      >
        <BackRight />
      </Box>
      <ToastContainer autoClose={6000} />
    </MainCard>
  );
}

ProfileCard.propTypes = { focusInput: PropTypes.func };
