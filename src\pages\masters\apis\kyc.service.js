import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

export const generateLink = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kyc-informations/generate-link/${newNgoId}`);
  return response?.data;
};
export const getLastestDataofNgo = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kyc-informations/latest-data/${newNgoId}`);
  return response?.data;
};
export const getKYCInformations = async (newNgoId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/kyc-informations?ngoId=${newNgoId}`);
  return response?.data;
};

export const updateKYCInformations = async (id, payload) => {
  const response = await axiosServices.put(`${API_BASE_URL}/kyc-informations/${id}`, payload);
  return response?.data;
};

export const getPanDetailsByPanNo = async (pan) => {
  const response = await axiosServices.get(`${API_BASE_URL}/temp-users/getPanDetails/byPanNo`, { params: { pan } });
  return response?.data;
};
