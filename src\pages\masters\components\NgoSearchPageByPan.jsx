import { useState } from 'react';
import { TextField, Box, Typography, CircularProgress, Button, Paper } from '@mui/material';
import { getPanDetailsByPanNo } from '../apis/kyc.service';
import MainCard from 'components/MainCard';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const NgoSearchPageByPan = () => {
  const [pan, setPan] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const handleSearch = async () => {
    if (!pan || pan.length < 5) {
      toast.warn('Please enter a valid PAN number.');
      return;
    }
    setLoading(true);
    setResult(null);
    try {
      const response = await getPanDetailsByPanNo(pan);
      if (response?.status && response?.data?.data) {
        setResult(response.data.data);
      } else {
        toast.error(response?.data?.message || 'No details found for this PAN.');
      }
    } catch (error) {
      toast.error('Error fetching PAN details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainCard>
      <form autoComplete="off">
        <Box sx={{ display: 'flex', gap: 2, mb: 2, width: '50%' }}>
          <TextField
            label="Enter PAN Number"
            variant="outlined"
            fullWidth
            value={pan}
            onChange={(e) => setPan(e.target.value.toUpperCase())}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleSearch();
              }
            }}
          />
          <Button variant="contained" type="button" onClick={handleSearch} disabled={loading || pan.length < 5}>
            Search
          </Button>
        </Box>
      </form>
      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3 }}>
          <CircularProgress size={40} sx={{ mr: 1 }} />
          <Typography variant="body1">Fetching PAN details...</Typography>
        </Box>
      ) : result ? (
        <Paper sx={{ p: 3, mt: 2, width: '50%' }} elevation={2}>
          <Typography variant="h6">PAN Details</Typography>
          <Typography><b>Full Name:</b> {result.full_name}</Typography>
          <Typography><b>Category:</b> {result.category}</Typography>
        </Paper>
      ) : null}
      <ToastContainer autoClose={6000} />
    </MainCard>
  );
};

export default NgoSearchPageByPan;
