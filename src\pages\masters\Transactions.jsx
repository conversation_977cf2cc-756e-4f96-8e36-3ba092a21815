import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Tabs, Tab, <PERSON>, Tooltip, Typography, Chip } from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CustomerTable from 'sections/apps/customer/CustomerTable';
import { getUserPermissions, formatIndianNumber } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';
import { fetchTransactionsService, deleteTransactionService } from './apis/transaction.service';
import dayjs from 'dayjs';
import TableActions from 'components/TableActions';
import CustomerTableWithPagination from 'sections/apps/customer/CustomerTableWithPagination';

export default function TransactionsTable() {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);

  // Role-based access
  const permissions = getUserPermissions(user);
  const canDelete = permissions?.Delete?.Transactions || false;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Tab configuration
  const tabs = [
    { label: 'Bucket Donations', type: 'genericViaMoney' },
    { label: 'NGO Donations', type: 'ngo' },
    { label: 'Campaign Donations', type: 'campaign' }
  ];

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, pageSize, activeTab]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const response = await fetchTransactionsService(user?.ngo_id, currentPage, pageSize, tabs[activeTab].type);
      setTransactions(response.transactions || []);
      setTotalCount(response.totalCount || 0);
      setTotalPages(response.totalPages || 1);
    } catch (error) {
      console.error('Failed to fetch Transactions:', error);
      toast.error('Failed to fetch Transactions');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  // Helper function to format amount with Indian comma formatting
  const formatAmount = (amount) => {
    if (!amount) return '₹0';
    return `₹${formatIndianNumber(amount)}`;
  };

  // Helper function to render bucket details with tooltip
  // const renderBucketDetails = (bucketDetails, type) => {
  //   if (!bucketDetails) return 'N/A';

  //   if (type === 'ngos' && bucketDetails.ngos?.length > 0) {
  //     const displayCount = 2;
  //     const ngos = bucketDetails.ngos;
  //     const displayNgos = ngos.slice(0, displayCount);
  //     const remainingCount = ngos.length - displayCount;

  //     return (
  //       <Box>
  //         {displayNgos.map((ngo, index) => (
  //           <Chip key={ngo.id} label={ngo.name} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
  //         ))}
  //         {remainingCount > 0 && (
  //           <Tooltip
  //             title={
  //               <Box>
  //                 {ngos.slice(displayCount).map((ngo) => (
  //                   <Typography key={ngo.id} variant="body2">
  //                     {ngo.name}
  //                   </Typography>
  //                 ))}
  //               </Box>
  //             }
  //           >
  //             <Chip label={`+${remainingCount} more`} size="small" color="primary" />
  //           </Tooltip>
  //         )}
  //       </Box>
  //     );
  //   }

  //   if (type === 'campaigns' && bucketDetails.campaigns?.length > 0) {
  //     const displayCount = 2;
  //     const campaigns = bucketDetails.campaigns;
  //     const displayCampaigns = campaigns.slice(0, displayCount);
  //     const remainingCount = campaigns.length - displayCount;

  //     return (
  //       <Box>
  //         {displayCampaigns.map((campaign, index) => (
  //           <Chip key={campaign.id} label={campaign.name} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
  //         ))}
  //         {remainingCount > 0 && (
  //           <Tooltip
  //             title={
  //               <Box>
  //                 {campaigns.slice(displayCount).map((campaign) => (
  //                   <Typography key={campaign.id} variant="body2">
  //                     {campaign.name}
  //                   </Typography>
  //                 ))}
  //               </Box>
  //             }
  //           >
  //             <Chip label={`+${remainingCount} more`} size="small" color="primary" />
  //           </Tooltip>
  //         )}
  //       </Box>
  //     );
  //   }

  //   return 'N/A';
  // };

  const renderBucketDetails = (bucketDetails, type) => {
    if (!bucketDetails) return 'N/A';

    if (type === 'ngos' && bucketDetails.ngos?.length > 0) {
      const displayCount = 2;
      const ngos = bucketDetails.ngos;
      const displayNgos = ngos.slice(0, displayCount);
      const remainingCount = ngos.length - displayCount;

      return (
        <Box>
          {displayNgos.map((ngo) => (
            <Tooltip key={ngo.id} title={ngo.name}>
              <Chip label={ngo.name} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
            </Tooltip>
          ))}
          {remainingCount > 0 && (
            <Tooltip
              title={
                <Box>
                  {ngos.slice(displayCount).map((ngo) => (
                    <Typography key={ngo.id} variant="body2">
                      {ngo.name}
                    </Typography>
                  ))}
                </Box>
              }
            >
              <Chip label={`+${remainingCount} more`} size="small" color="primary" />
            </Tooltip>
          )}
        </Box>
      );
    }

    if (type === 'campaigns' && bucketDetails.campaigns?.length > 0) {
      const displayCount = 2;
      const campaigns = bucketDetails.campaigns;
      const displayCampaigns = campaigns.slice(0, displayCount);
      const remainingCount = campaigns.length - displayCount;

      return (
        <Box>
          {displayCampaigns.map((campaign) => (
            <Tooltip key={campaign.id} title={campaign.name}>
              <Chip label={campaign.name} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
            </Tooltip>
          ))}
          {remainingCount > 0 && (
            <Tooltip
              title={
                <Box>
                  {campaigns.slice(displayCount).map((campaign) => (
                    <Typography key={campaign.id} variant="body2">
                      {campaign.name}
                    </Typography>
                  ))}
                </Box>
              }
            >
              <Chip label={`+${remainingCount} more`} size="small" color="primary" />
            </Tooltip>
          )}
        </Box>
      );
    }

    return 'N/A';
  };

  const handleDelete = async (id) => {
    if (!canDelete) {
      toast.error('You do not have permission to delete Transactions.');
      return;
    }
    const confirmDelete = window.confirm('Are you sure you want to delete this Transaction?');
    if (confirmDelete) {
      try {
        await deleteTransactionService(id);
        toast.success('Transaction deleted successfully!');
        fetchTransactions();
      } catch (error) {
        console.error('Failed to delete Transaction:', error);
        toast.error('Failed to delete Transaction');
      }
    }
  };

  // Dynamic columns based on active tab
  const getColumns = () => {
    const currentTabType = tabs[activeTab].type;
    const isAdminUser = user.roleInfo.name.startsWith('DR');

    // Base columns that appear in all tabs
    const baseColumns = [
      {
        accessorKey: 'amount',
        header: 'Amount',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_after_charges',
        header: 'DR Tip',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'userInfo.fullname',
        header: 'Donor Name',
        showByDefault: true,
        cell: (cell) => cell.getValue() || 'N/A'
      },
      {
        accessorKey: 'is_donor_within_maharashtra',
        header: 'Donor Location',
        showByDefault: true,
        cell: (cell) => (
          <Chip
            label={cell.getValue() ? 'Inside Maharashtra' : 'Outside Maharashtra'}
            color={cell.getValue() ? 'success' : 'default'}
            size="small"
          />
        )
      },
      {
        accessorKey: 'is_ngo_within_maharashtra',
        header: 'NGO Location',
        showByDefault: true,
        cell: (cell) => (
          <Chip
            label={cell.getValue() ? 'Inside Maharashtra' : 'Outside Maharashtra'}
            color={cell.getValue() ? 'success' : 'default'}
            size="small"
          />
        )
      },
      {
        accessorKey: 'orderInfo',
        header: 'Order ID',
        showByDefault: true,
        cell: (cell) => {
          const orderInfo = cell.getValue();
          return orderInfo ? `${orderInfo.order_prefix}${orderInfo.order_id}` : 'N/A';
        }
      },
      {
        accessorKey: 'impact_created',
        header: 'Impact Created',
        showByDefault: true,
        cell: (cell) => `${cell.getValue() || 0} lives`
      }
    ];

    const amountBreakdownColumns = [
      {
        accessorKey: 'payment_gateway_charges',
        header: 'Gateway Charges',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'ngo_platform_convenience_fee',
        header: 'Platform Fee',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'ngo_net_donation',
        header: 'NGO Net Donation',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_percent',
        header: 'Tip %',
        showByDefault: true,
        cell: (cell) => (cell.getValue() ? `${(cell.getValue() * 100).toFixed(2)}%` : 'N/A')
      },
      {
        accessorKey: 'gateway_charges_percent',
        header: 'Gateway %',
        showByDefault: true,
        cell: (cell) => (cell.getValue() ? `${(cell.getValue() * 100).toFixed(2)}%` : 'N/A')
      },
      {
        accessorKey: 'dr_tip_after_charges',
        header: 'Total Tip ',
        showByDefault: true,
        cell: (cell) => (cell.getValue() ? formatAmount(cell.getValue()) : 'N/A')
      },
      {
        accessorKey: 'dr_tip_base_amount',
        header: 'Tip Base Amount',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_total_gst',
        header: 'Tip GST (Total)',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_cgst',
        header: 'CGST',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_sgst',
        header: 'SGST',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_igst',
        header: 'IGST',
        showByDefault: true,
        cell: (cell) => formatAmount(cell.getValue())
      },
      {
        accessorKey: 'dr_tip_gst_type',
        header: 'GST Type',
        showByDefault: true,
        cell: (cell) => cell.getValue() || 'N/A'
      }
    ];

    // Add conditional columns based on tab type
    const conditionalColumns = [];

    if (currentTabType === 'ngo' && isAdminUser) {
      conditionalColumns.push({
        accessorKey: 'ngoInfo.name',
        header: 'NGO Name',
        showByDefault: true,
        cell: (cell) => cell.getValue() || 'N/A'
      });
    }

    if (currentTabType === 'campaign') {
      conditionalColumns.push({
        accessorKey: 'campaignInfo.name',
        header: 'Campaign Name',
        showByDefault: true,
        cell: (cell) => cell.getValue() || 'N/A'
      });
    }

    if (currentTabType === 'genericViaMoney') {
      conditionalColumns.push(
        {
          accessorKey: 'bucketDetails.name',
          id: 'bucketName', // 👈 unique id
          header: 'Bucket Name',
          showByDefault: true,
          cell: (cell) => cell.getValue() || 'N/A'
        },
        {
          accessorKey: 'bucketDetails',
          id: 'bucketNgos', // 👈 unique id
          header: 'NGOs',
          showByDefault: true,
          cell: (cell) => renderBucketDetails(cell.getValue(), 'ngos')
        },
        {
          accessorKey: 'bucketDetails',
          id: 'bucketCampaigns', // 👈 unique id
          header: 'Campaigns',
          showByDefault: true,
          cell: (cell) => renderBucketDetails(cell.getValue(), 'campaigns')
        }
      );
    }

    // Add timestamp and action columns
    const endColumns = [
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        showByDefault: true,
        cell: (cell) => dayjs(cell.getValue()).format('DD-MM-YYYY hh:mm A')
      },
      {
        id: 'actions',
        header: 'Action',
        cell: (cell) => <TableActions showEdit={false} handleDeleteClick={handleDelete} cell={cell} />
      }
    ];

    return [...baseColumns, ...conditionalColumns,...amountBreakdownColumns, ...endColumns];
  };

  const columns = getColumns();

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  return (
    <div>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="transaction tabs">
          {tabs.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
      </Box>

      <CustomerTableWithPagination
        data={transactions}
        columns={columns}
        category={'Transaction'}
        currentPage={currentPage}
        totalCount={totalCount}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        loading={loading}
      />
      <ToastContainer />
    </div>
  );
}
