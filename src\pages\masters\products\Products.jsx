import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Divider,
  Grid,
  CircularProgress,
  Box,
  Tabs,
  Tab,
  Tooltip,
  IconButton
} from '@mui/material';
import { toast, ToastContainer } from 'react-toastify';
import { API_BASE_URL } from 'api/categories.service';
import TableActions from 'components/TableActions';
import ProductCard from 'components/cards/e-commerce/ProductCard';
import { ClockCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';

import dayjs from 'dayjs';
import useAuth from 'hooks/useAuth';
import { deleteProduct, getProducts, patchProduct } from './product.service';
import CustomProductTable from 'sections/apps/customer/CustomProductTable';

import { getNGOSBySearchTerm } from 'api/campaigns.service';
import MainCard from 'components/MainCard';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { PRODUCT_STATUS_LIST } from 'utils/statusconstans';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Link } from 'react-router-dom';

const Products = () => {
  const { user } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  //tabs
  const statusList = PRODUCT_STATUS_LIST;
  const [tabValue, setTabValue] = useState(0);
  const [statusCounts, setStatusCounts] = useState({});
  const [searchParams] = useSearchParams();

  const alertStatus = searchParams.get('alertStatus') ? searchParams.get('alertStatus') : 'All';
  //pagination
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const navigate = useNavigate();

  const [viewMode, setViewMode] = useState('table');

  const [ngoList, setNgoList] = useState([]);

  useEffect(() => {
    const currentStatus = statusList[tabValue];
    fetchProducts(currentStatus);
  }, [currentPage, pageSize]);

  const fetchProducts = async (status) => {
    setIsLoading(true);
    try {
      const response = await getProducts(user?.ngo_id, status, currentPage, pageSize);

      setProducts(response.products);
      setStatusCounts(response?.statusCountsMap);
      setTotalCount(response?.totalCount);

      if (alertStatus !== 'All') {
        setTabValue(statusList.findIndex((st) => st === alertStatus));
      }
    } catch (error) {
      toast.error('Failed to fetch Products');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) return;
    setLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceFetch = setTimeout(fetchNgos(searchTerm), 500); // Debounce the API call

    return () => clearTimeout(debounceFetch); // Cleanup
  }, [searchTerm]);

  const handleDelete = async (id) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this product?');
    if (confirmDelete) {
      try {
        await deleteProduct(id);
        toast.success('Product deleted successfully!');
        fetchProducts();
      } catch (error) {
        toast.error('Failed to delete product');
      }
    }
  };

  const handleLiveUpdate = async (id, status) => {
    const confirmLive = window.confirm(`Are you sure you want to make this product ${status}?`);
    if (confirmLive) {
      try {
        const response = await patchProduct(id, { status: status });
        if (response?.id) {
          toast.success('Selected product has been Verified!');
          fetchProducts(status == 'Inactive' ? 'Verified' : 'In Review');
        }
      } catch (error) {
        console.error('Failed to verify the product:', error);
        toast.error('Failed to set the product as verified');
      }
    }
  };

  const handleEditClick = (id) => {
    navigate(`/masters/products/edit/${id}`);
  };
  const handleAddClick = () => {
    navigate(`/masters/products/add`);
  };

  const columns = [
    { accessorKey: 'product_image', header: 'Product Image', showByDefault: true },
    {
      accessorKey: 'name',
      header: 'Name',
      showByDefault: true,
      cell: ({ cell }) => {
        return (
          <Link className="ngo-link" to={`/masters/products/edit/${cell.row.original.id}`}>
            {cell.row.original.name}
          </Link>
        );
      }
    },
    { accessorKey: 'description', header: 'Description', showByDefault: true },
    { accessorKey: 'price', header: 'Price', showByDefault: true },

    {
      accessorKey: 'discount',
      header: 'Discount',
      showByDefault: true,
      cell: ({ cell }) => {
        const value = cell.row.original.discount;
        return <span>{value && value != 0 ? `${value}%` : '-'}</span>;
      }
    },
    {
      accessorKey: 'discountedPrice',
      header: 'Discounted Price',
      showByDefault: true,
      cell: ({ cell }) => {
        const value = cell.row.original.discountedPrice;

        return <span>{value && value != 0 ? value : '-'}</span>;
      }
    },
    { accessorKey: 'count', header: 'Count', showByDefault: true },

    { accessorKey: 'unit_of_measure', header: 'Unit of Measure', showByDefault: true },
    {
      accessorKey: 'collectionInfo.name',
      header: 'Collection Name',
      showByDefault: true,
      cell: (cell) => {
        const collectionName = cell.row.original.collectionInfo?.name || 'N/A';
        return <span>{collectionName}</span>;
      }
    },

    { accessorKey: 'isStock', header: 'In Stock', showByDefault: true },

    ...(user?.roleInfo.name.startsWith('DR')
      ? [
          {
            accessorKey: 'ngoInfo.name',
            header: 'NGO Name',
            showByDefault: true,
            cell: (cell) => {
              const ngoName = cell.row.original.ngoInfo?.name || 'N/A';
              return <span>{ngoName}</span>;
            }
          }
        ]
      : []),
    { accessorKey: 'status', header: 'Status' },
    // { accessorKey: 'collection_id', header: 'Collection ID' },

    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.createdAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated At',
      cell: (cell) => {
        const formattedDate = dayjs(cell.row.original.updatedAt).format('DD-MM-YYYY hh:mm A');
        return <span>{formattedDate}</span>;
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: (cell) => {
        const id = cell.row.original.id;
        return (
          <Box display="flex" flexDirection="row">
            <TableActions
              handleEditClick={() => handleEditClick(id)}
              cell={cell}
              handleDeleteClick={() => handleDelete(cell.row.original.id)}
            />
            {tabValue === 1 && user?.roleInfo.name == 'DR_Management' && (
              <Tooltip title="Make it verified.">
                <IconButton
                  color="success"
                  size="medium"
                  onClick={() => {
                    handleLiveUpdate(id, 'Verified');
                  }}
                  style={{ marginLeft: '8px' }}
                >
                  <ClockCircleOutlined />
                </IconButton>
              </Tooltip>
            )}
            {tabValue === 4 && user?.roleInfo.name == 'DR_Management' && (
              <Tooltip title="Mark as inactive.">
                <IconButton
                  color="error"
                  size="medium"
                  onClick={() => {
                    handleLiveUpdate(id, 'Inactive');
                  }}
                  style={{ marginLeft: '8px' }}
                >
                  <StopOutlined />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        );
      }
    }
  ];

  //tabs
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setCurrentPage(1);
    const selectedStatus = statusList[newValue];

    fetchProducts(selectedStatus);
  };

  function TabPanel({ children, value, index, ...other }) {
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`product-details-tabpanel-${index}`}
        aria-labelledby={`product-details-tab-${index}`}
        {...other}
      >
        {value === index && <Box>{children}</Box>}
      </div>
    );
  }

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
  };

  return (
    <MainCard>
      <Stack spacing={3}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Tabs
            value={tabValue}
            indicatorColor="primary"
            onChange={handleTabChange}
            aria-label="product description tabs example"
            variant="scrollable"
          >
            {statusList.map((sl, index) => {
              const count = statusCounts && statusCounts[sl] ? statusCounts[sl] : 0;
              return <Tab key={index} component={Link} to="#" label={`${sl} (${count})`} />;
            })}
          </Tabs>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Button variant="contained" color="primary" size="small" startIcon={<PlusOutlined />} onClick={handleAddClick}>
              Add Product
            </Button>

            {/* <FormControl sx={{ minWidth: 120 }}>
              <InputLabel id="view-mode-label">View Mode</InputLabel>
              <Select
                labelId="view-mode-label"
                id="view-mode-select"
                size="small"
                value={viewMode}
                label="View Mode"
                onChange={(e) => setViewMode(e.target.value)}
              >
                <MenuItem value="table">Table</MenuItem>
                <MenuItem value="card">Card</MenuItem>
              </Select>
            </FormControl> */}
          </Stack>
        </Stack>
        {statusList.map((sl, index) => (
          <TabPanel value={tabValue} index={index} key={index}>
            {viewMode === 'table' ? (
              isLoading ? (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center'
                  }}
                >
                  <CircularProgress size={40} color="primary" />
                  <Box sx={{ marginTop: 2, fontSize: '16px', color: 'text.secondary' }}>Loading your products...</Box>
                </Box>
              ) : (
                <CustomProductTable
                  data={products}
                  columns={columns}
                  modalToggler={handleAddClick}
                  category="Product"
                  statusList={statusList}
                  totalCount={totalCount}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                />
              )
            ) : (
              <Grid container spacing={3}>
                {products.map((item) => {
                  const imageUrl = `${BASE_URL}/fetchProductImages/${item.imageName}`;
                  return (
                    <Grid item xs={12} sm={6} md={4} key={item.id}>
                      <ProductCard
                        item={item}
                        imageUrl={imageUrl}
                        ngoName={item?.ngoInfo?.name}
                        collectionName={item?.collectionInfo?.name}
                      />
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </TabPanel>
        ))}
      </Stack>
      <ToastContainer />
    </MainCard>
  );
};

export default Products;
