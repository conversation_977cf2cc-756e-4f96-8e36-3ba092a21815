import { useState } from 'react';
import axiosServices from 'utils/axios';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

/**
 * Generate Campaign Donor Report
 * @param {number} campaignId - Required campaign ID
 * @param {string|null} startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateCampaignDonorReport = async (campaignId, startDate = null, endDate = null) => {
  try {
    const params = new URLSearchParams();
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }

    const queryString = params.toString();
    const url = `${API_BASE_URL}/reports/campaign-donors/${campaignId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await axiosServices.get(url);
    return response.data;
  } catch (error) {
    console.error('Error generating campaign donor report:', error);
    throw error;
  }
};

/**
 * Generate Event Volunteer Report
 * @param {number} eventId - Required event ID
 * @param {string|null} startDate - Optional start date (YYYY-MM-DD)
 * @param {string|null} endDate - Optional end date (YYYY-MM-DD)
 * @returns {Promise} API response with download URL
 */
export const generateEventVolunteerReport = async (eventId, startDate = null, endDate = null) => {
  try {
    const params = new URLSearchParams();
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }

    const queryString = params.toString();
    const url = `${API_BASE_URL}/reports/event-volunteers/${eventId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await axiosServices.get(url);
    return response.data;
  } catch (error) {
    console.error('Error generating event volunteer report:', error);
    throw error;
  }
};

/**
 * Download file from URL
 * @param {string} downloadUrl - The download URL from API response
 * @param {string} fileName - The file name from API response
 */
export const downloadFile = (downloadUrl, fileName) => {
  try {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

/**
 * Custom hook for Individual report generation
 */
export const useIndividualReports = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateAndDownload = async (reportFunction, ...args) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await reportFunction(...args);
      
      if (response?.success && response?.data?.downloadUrl) {
        downloadFile(response.data.downloadUrl, response.data.fileName);
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to generate report');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const generateCampaignReport = async (campaignId, startDate = null, endDate = null) => {
    return generateAndDownload(generateCampaignDonorReport, campaignId, startDate, endDate);
  };

  const generateEventReport = async (eventId, startDate = null, endDate = null) => {
    return generateAndDownload(generateEventVolunteerReport, eventId, startDate, endDate);
  };

  return { 
    generateCampaignReport, 
    generateEventReport, 
    loading, 
    error 
  };
};
