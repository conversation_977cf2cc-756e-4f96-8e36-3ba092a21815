// third-party
import { FormattedMessage } from 'react-intl';

// project-imports
import { handlerCustomerDialog } from 'api/customer';
import { NavActionType } from 'config';

// assets
import BuildOutlined from '@ant-design/icons/BuildOutlined';
import CalendarOutlined from '@ant-design/icons/CalendarOutlined';
import CustomerServiceOutlined from '@ant-design/icons/CustomerServiceOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import ShoppingCartOutlined from '@ant-design/icons/ShoppingCartOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import AppstoreAddOutlined from '@ant-design/icons/AppstoreAddOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import LinkOutlined from '@ant-design/icons/LinkOutlined';
import { useEffect } from 'react';
import {
  AuditOutlined,
  BarChartOutlined,
  ContainerOutlined,
  FlagOutlined,
  FormOutlined,
  FundProjectionScreenOutlined,
  MailOutlined,
  NotificationOutlined,
  ProfileOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  SecurityScanOutlined,
  SettingOutlined,
  TransactionOutlined,
  TruckOutlined,
  UserAddOutlined,
  UsergroupDeleteOutlined
} from '@ant-design/icons';
import { getSessionStorageItem } from 'utils/permissionUtils';

// type

// icons
const icons = {
  BuildOutlined,
  CalendarOutlined,
  CustomerServiceOutlined,
  MessageOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreAddOutlined,
  FileTextOutlined,
  PlusOutlined,
  LinkOutlined,
  FlagOutlined,
  UsergroupDeleteOutlined
};

//retrieve from local storage
const user = getSessionStorageItem('user');
const sidebarPermissions = user?.roleInfo && user?.roleInfo?.permissions ? user?.roleInfo?.permissions?.ShowSidebar : [];

// ==============================|| MENU ITEMS - MASTERS ||============================== //

const allManageItems = [
  {
    id: 'NGOS',
    title: <FormattedMessage id="NGOS" />,
    type: 'item',
    url: '/masters/ngos',
    icon: UsergroupDeleteOutlined,
    breadcrumbs: false
  },

  {
    id: 'Campaigns',
    title: <FormattedMessage id="Campaigns" />,
    type: 'item',
    url: '/masters/campaigns',
    icon: FlagOutlined,
    breadcrumbs: true
  },
  {
    id: 'Events',
    title: <FormattedMessage id="Events" />,
    type: 'item',
    url: '/masters/events',
    icon: MessageOutlined,
    breadcrumbs: true
  },
  {
    id: 'myProfile',
    title: <FormattedMessage id="myProfile" />,
    type: 'item',
    url: '/apps/profiles/account/basic',
    icon: UserAddOutlined,
    breadcrumbs: false
  },
  {
    id: 'Products',
    title: <FormattedMessage id="Products" />,
    type: 'item',
    icon: TruckOutlined,
    url: '/masters/products',
    breadcrumbs: true
  },
  {
    id: 'SearchNgo',
    title: <FormattedMessage id="SearchNgo" />,
    type: 'item',
    icon: SearchOutlined,
    url: '/ngoSearchPage/search',
    breadcrumbs: true
  },
  {
    id: 'SearchPan',
    title: <FormattedMessage id="SearchPan" />,
    type: 'item',
    icon: SearchOutlined,
    url: '/ngoSearchPage/searchBypan',
    breadcrumbs: true
  },

  {
    id: 'Transactions',
    title: <FormattedMessage id="Transactions" />,
    type: 'item',
    icon: TransactionOutlined,
    url: '/masters/transactions',
    breadcrumbs: true
  },
  {
    id: 'Users',
    title: <FormattedMessage id="Users" />,
    type: 'item',
    icon: UserOutlined,
    url: '/masters/users',
    breadcrumbs: true
  },
  {
    id: 'NewsLetters',
    title: <FormattedMessage id="NewsLetters" />,
    type: 'item',
    icon: AuditOutlined,
    url: '/masters/newsletters',
    breadcrumbs: true
  },
  {
    id: 'Collections',
    title: <FormattedMessage id="Collections" />,
    type: 'item',
    icon: ContainerOutlined,
    url: '/masters/collections',
    breadcrumbs: true
  },

  {
    id: 'Orders',
    title: <FormattedMessage id="Orders" />,
    type: 'item',
    icon: AuditOutlined,
    url: '/channels/orders',
    breadcrumbs: true
  }
];

// Filter it
const filteredMasterItems = allManageItems.filter((item) => {
  if (sidebarPermissions[item.id]) {
    if (item.children) {
      item.children = item.children.filter((child) => sidebarPermissions[child.id]);
    }
    return true;
  }
  return false;
});

if (user?.roleInfo?.name === 'NGO_Management' || user?.roleInfo?.name === 'NGO_Staff') {
  filteredMasterItems.push(
    {
      id: 'ngoProfile',
      title: <FormattedMessage id="ngoProfile" />,
      type: 'item',
      url: '/apps/profiles/account/ngoprofile',
      icon: UserAddOutlined,
      breadcrumbs: false
    },
    {
      id: 'FeedBacks',
      title: <FormattedMessage id="FeedBacks" />,
      type: 'item',
      url: '/masters/feedback-bugs',
      icon: FormOutlined,
      breadcrumbs: true
    },
    {
      id: 'Reports',
      title: <FormattedMessage id="Reports" />,
      type: 'item',
      url: '/dashboard/reports',
      icon: BarChartOutlined,
      breadcrumbs: false
    }
  );
}
if (user?.roleInfo?.name === 'NGO_Management' || user?.roleInfo?.name === 'DR_Staff' || user?.roleInfo?.name === 'DR_Management') {
  filteredMasterItems.push({
    id: 'StaffMembers',
    title: <FormattedMessage id="StaffMembers" />,
    type: 'item',
    url: '/masters/staff-members',
    icon: UserOutlined,
    breadcrumbs: true
  });
}
if (user?.roleInfo?.name === 'DR_Staff' || user?.roleInfo?.name === 'DR_Management') {
  filteredMasterItems.push(
    {
      id: 'Reports',
      title: <FormattedMessage id="Reports" />,
      type: 'item',
      url: '/dashboard/reports',
      icon: BarChartOutlined,
      breadcrumbs: false
    },
    {
      id: 'ProfileQueries',
      title: <FormattedMessage id="ProfileQueries" />,
      type: 'item',
      url: '/masters/profile-queries',
      icon: AuditOutlined,
      breadcrumbs: true
    },
    {
      id: 'FeedBacks',
      title: <FormattedMessage id="FeedBacks" />,
      type: 'item',
      url: '/masters/feedback-bugs',
      icon: FormOutlined,
      breadcrumbs: true
    },
    {
      id: 'UserNotification',
      title: <FormattedMessage id="UserNotification" />,
      type: 'item',
      url: '/channels/user-notifications',
      icon: NotificationOutlined,
      breadcrumbs: true
    }
  );
}
if (user?.roleInfo?.name === 'DR_Management') {
  filteredMasterItems.push(
    {
      id: 'AuditLogs',
      title: <FormattedMessage id="AuditLogs" />,
      type: 'item',
      url: '/masters/logs',
      icon: AuditOutlined,
      breadcrumbs: true
    },
    {
      id: 'Buckets',
      title: <FormattedMessage id="Buckets" />,
      type: 'item',
      url: '/channels/buckets',
      icon: ProfileOutlined,
      breadcrumbs: true
    }
  );
}
// Master menu configuration with filtered items
const manage = {
  id: 'manage',
  title: <FormattedMessage id="manage" />,
  icon: AppstoreAddOutlined,
  type: 'group',
  children: filteredMasterItems // Use filtered items here
};

export default manage;
