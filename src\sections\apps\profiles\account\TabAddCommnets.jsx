import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';
import axios from 'axios';
import dayjs from 'dayjs';
import { API_BASE_URL } from 'api/campaigns.service';
import {
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextareaAutosize,
  TextField,
  Typography,
  Autocomplete,
  List,
  ListItemText,
  ListItem,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  CircularProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';

import useAuth from 'hooks/useAuth';
import { NGO_STATUS_LIST } from 'utils/statusconstans';
import { getLoggedInNGOInfo, updateUsingPatchNGO } from '../profile.service';
import FeedsCard from 'sections/widget/data/FeedsCard';
import { addJournals, addNotifications, getJournals, getNotifications } from 'pages/masters/apis/notification.service';
import { ContentState, EditorState, convertFromRaw, convertToRaw } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import { generateLink, getLastestDataofNgo, getKYCInformations, updateKYCInformations } from 'pages/masters/apis/kyc.service';
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

export default function TabProfile() {
  const [commentEditing, setCommentEditing] = useState(true);
  const [journalEditing, setJournalEditing] = useState(true);
  const { user: userInfo } = useAuth();
  const [loading, setLoading] = useState(false);
  const [kycLoading, setKycLoading] = useState(false);
  const [link, setLink] = useState('');
  const [latestNgoData, setLatestNgoData] = useState(null);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isApproving, setIsApproving] = useState(false);

  const [comments, setComments] = useState([]);
  const [journals, setJournals] = useState([]);
  const [commentseditorState, setCommentsEditorState] = useState(() => EditorState.createEmpty());
  const [journalseditorState, setJournalsEditorState] = useState(() => EditorState.createEmpty());

  const { newNgoId } = useParams();

  const navigate = useNavigate();
  const [ngoDetails, setNgoDetails] = useState({
    description: '',
    ngo_id: ''
  });
  const [journalDetails, setjournalDetails] = useState({
    description: '',
    ngo_id: ''
  });
  const [ngoInfo, setNgoInfo] = useState({
    physical_evidence: '',
    documents_verified: '',
    ngo_status: ''
  });

  // KYC Verification History states
  const [kycHistory, setKycHistory] = useState([]);
  const [kycHistoryLoading, setKycHistoryLoading] = useState(false);
  const [openRejectionDialog, setOpenRejectionDialog] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [selectedKycId, setSelectedKycId] = useState(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState({ id: null, status: null });

  useEffect(() => {
    fetchngoDetails();
    fetchComments(newNgoId);
    fetchJournals(newNgoId);
    fetchLatestNgoData(newNgoId);
    fetchKycHistory(newNgoId);
  }, []);

  const fetchComments = async (newNgoId) => {
    try {
      const response = await getNotifications(newNgoId, 'ngo');
      setComments(response || []);
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast.error('Failed to fetch comments');
    }
  };
  const fetchJournals = async (newNgoId) => {
    try {
      const response = await getJournals(newNgoId);

      setJournals(response || []);
    } catch (error) {
      console.error('Failed to fetch journals:', error);
      toast.error('Failed to fetch journals');
    }
  };

  const fetchLatestNgoData = async (newNgoId) => {
    try {
      const response = await getLastestDataofNgo(newNgoId);
      setLatestNgoData(response);
    } catch (error) {
      console.error('Failed to fetch latest NGO data:', error);
      // Don't show toast error for this as it's not critical
    }
  };

  const fetchKycHistory = async (ngoId) => {
    try {
      setKycHistoryLoading(true);
      const response = await getKYCInformations(ngoId);
      // Sort by createdAt in descending order (latest first)
      const sortedHistory = response.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setKycHistory(sortedHistory || []);
    } catch (error) {
      console.error('Failed to fetch KYC history:', error);
      toast.error('Failed to fetch KYC verification history');
      setKycHistory([]);
    } finally {
      setKycHistoryLoading(false);
    }
  };

  // Helper function to check if KYC button should be disabled
  const isKycButtonDisabled = () => {
    if (kycLoading) return true;

    // If no KYC history, allow generating link
    if (!kycHistory || kycHistory.length === 0) return false;

    // Get the latest KYC record (first in array since it's sorted by latest first)
    const latestKycRecord = kycHistory[0];

    // Disable if latest record status is Pending, In Review, or Approved
    // Enable if latest record is Rejected (so they can generate a new link)
    const disabledStatuses = ['Pending', 'In Review', 'Approved'];
    return disabledStatuses.includes(latestKycRecord.status);
  };

  // Helper function to get tooltip message for disabled button
  const getKycButtonTooltip = () => {
    if (kycLoading) return 'Generating KYC link...';

    // If no KYC history, allow generating link
    if (!kycHistory || kycHistory.length === 0) {
      return 'Generate KYC verification link for this NGO';
    }

    // Get the latest KYC record
    const latestKycRecord = kycHistory[0];
    const disabledStatuses = ['Pending', 'In Review', 'Approved'];

    if (disabledStatuses.includes(latestKycRecord.status)) {
      return `Cannot generate KYC link. Latest KYC verification status is '${latestKycRecord.status}'.`;
    }

    if (latestKycRecord.status === 'Rejected') {
      return 'Previous KYC verification was rejected. You can generate a new link.';
    }

    return 'Generate KYC verification link for this NGO';
  };

  const fetchngoDetails = async () => {
    try {
      if (newNgoId) {
        const response = await getLoggedInNGOInfo(newNgoId);
        setNgoInfo({ ...response, date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null });
        return;
      }
      const response = await getLoggedInNGOInfo(userInfo?.ngo_id);
      setNgoInfo({ ...response, date_of_establishment: response?.date_of_establishment ? dayjs(response?.date_of_establishment) : null });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to fetch user details');
    }
  };
  const handleNgoInfoChange = (e) => {
    const { name, value } = e.target;
    setNgoInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleNgoInfoSubmit = async () => {
    try {
      if (!ngoInfo.physical_evidence) {
        toast.error('Visited Onsite is required.');
        return;
      }
      if (!ngoInfo.documents_verified) {
        toast.error('Documents Verified field is required.');
        return;
      }
      if (!ngoInfo.ngo_status) {
        toast.error('NGO Status is required.');
        return;
      }
      ngoInfo.verified_on = ngoInfo.ngo_status === 'Verified' ? dayjs().format('YYYY-MM-DD HH:mm:ss') : null;

      ngoInfo.last_status = ngoInfo.ngo_status;

      const response = await updateUsingPatchNGO(newNgoId, ngoInfo);

      if (response.id) {
        fetchComments();
        fetchngoDetails();
        toast.success('NGO Information updated successfully!');
      } else {
        throw new Error('Unexpected response');
      }
      setNgoInfo({
        physical_evidence: '',
        documents_verified: '',
        ngo_status: ''
      });
    } catch (error) {
      console.error('Error updating NGO Information:', error);
      toast.error('Failed to update NGO Information');
    }
  };
  const handleGenerateKYCLink = async () => {
    try {
      setKycLoading(true);
      const response = await generateLink(newNgoId);
      if (response.uniqueLink) {
        setLink(response.uniqueLink);
        toast.success('KYC link has been generated and sent to the registered email address successfully.');
        // Refresh the latest NGO data to update button state
        await fetchLatestNgoData(newNgoId);
      } else {
        toast.error('Failed to generate link');
      }
    } catch (error) {
      console.error('Failed to generate link:', error);
      toast.error('Failed to generate link');
    } finally {
      setKycLoading(false);
    }
  };

  const handleCommentInputChange = (e) => {
    const { name, value } = e.target;
    setNgoDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleJournalInputChange = (e) => {
    const { name, value } = e.target;
    setjournalDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const contentState = commentseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('The required field is empty. Please fill it out.');
      return;
    }

    const payload = {
      ngo_id: newNgoId,
      sender_id: userInfo.id,
      description,
      messageRead: 'no',
      type: 'ngo',
      type_id: newNgoId
    };

    try {
      setLoading(true);

      const response = await addNotifications(payload);

      if (response.status) {
        toast.success('Notification sent successfully!');
        setCommentsEditorState(EditorState.createEmpty());
        fetchComments(newNgoId);
      } else {
        throw new Error('Unexpected response');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      if (error?.message) toast.error('Failed to send notification');
      //   window.location.reload();
    } finally {
      setLoading(false);
    }
  };

  const handleJournalSubmit = async (e) => {
    e.preventDefault();

    const contentState = journalseditorState.getCurrentContent();
    const description = JSON.stringify(convertToRaw(contentState));

    if (!contentState.hasText()) {
      toast.error('The required field is empty. Please fill it out.');
      return;
    }

    const payload = {
      ngo_id: newNgoId,
      sender_id: userInfo.id,
      description
    };

    try {
      const response = await addJournals(payload);

      if (response.status) {
        toast.success('Note added successfully!');
        setJournalsEditorState(EditorState.createEmpty());
        fetchJournals(newNgoId);
      } else {
        throw new Error('Unexpected response');
      }
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    }
  };

  const handleEditClick = (e) => {
    e.preventDefault();
    setCommentEditing(true);
  };

  const handleCancelEdit = () => {
    setCommentEditing(true);
    setNgoDetails({ description: '', ngo_id: '' });
  };
  const handleJournalEditClick = (e) => {
    e.preventDefault();
    setJournalEditing(true);
  };

  const handleJournalCancelEdit = () => {
    setJournalEditing(true);
    setjournalDetails({ description: '', ngo_id: '' });
  };

  // KYC Status Update Functions
  const handleKycStatusUpdate = (id, status) => {
    if (status === 'Rejected') {
      setSelectedKycId(id);
      setOpenRejectionDialog(true);
      return;
    }

    if (status === 'Approved') {
      setConfirmAction({ id, status });
      setOpenConfirmDialog(true);
      return;
    }
  };

  const handleKycReject = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please enter a reason for rejection.');
      return;
    }

    try {
      setIsRejecting(true);
      const payload = {
        status: 'Rejected',
        rejectedReason: rejectionReason
      };

      const response = await updateKYCInformations(selectedKycId, payload);

      if (response) {
        toast.success('KYC verification rejected successfully.');
        setRejectionReason('');
        fetchKycHistory(newNgoId);
      } else {
        toast.error('Failed to reject KYC verification.');
      }
    } catch (error) {
      console.error('Error rejecting KYC verification:', error);
      toast.error('An error occurred while rejecting the KYC verification.');
    } finally {
      setOpenRejectionDialog(false);
      setSelectedKycId(null);
      setIsRejecting(false);
    }
  };

  const handleKycApprove = async () => {
    try {
      setIsApproving(true);
      const payload = { status: 'Approved' };

      const response = await updateKYCInformations(confirmAction.id, payload);

      if (response) {
        toast.success('KYC verification approved successfully.');
        fetchKycHistory(newNgoId);
      } else {
        toast.error('Failed to approve KYC verification.');
      }
    } catch (error) {
      console.error('Error approving KYC verification:', error);
      toast.error('An error occurred while approving the KYC verification.');
    } finally {
      setOpenConfirmDialog(false);
      setConfirmAction({ id: null, status: null });
      setIsApproving(false);
    }
  };

  return (
    <Card>
      <CardContent>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Visited Onsite</InputLabel>
              <Select value={ngoInfo.physical_evidence} name="physical_evidence" onChange={handleNgoInfoChange}>
                <MenuItem value="yes">Yes</MenuItem>
                <MenuItem value="no">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={2}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Document Verified</InputLabel>
              <Select value={ngoInfo.documents_verified} name="documents_verified" onChange={handleNgoInfoChange}>
                <MenuItem value="yes">Yes</MenuItem>
                <MenuItem value="no">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select value={ngoInfo.ngo_status} name="ngo_status" onChange={handleNgoInfoChange}>
                {NGO_STATUS_LIST.map((status, index) => (
                  <MenuItem key={index} value={status}>
                    {status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button variant="contained" color="primary" onClick={handleNgoInfoSubmit}>
                Update NGO Verification
              </Button>
              <Tooltip title={getKycButtonTooltip()} arrow>
                <span>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleGenerateKYCLink}
                    disabled={isKycButtonDisabled()}
                    startIcon={kycLoading && <CircularProgress size={20} color="inherit" />}
                  >
                    {kycLoading ? 'Generating...' : 'Generate KYC Link'}
                  </Button>
                </span>
              </Tooltip>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          {/* Video KYC Verification History Section */}
          <Grid item xs={12} sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Video KYC Verification History
            </Typography>

            {kycHistoryLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Loading KYC verification history...</Typography>
              </Box>
            ) : kycHistory?.length === 0 ? (
              <Box sx={{ py: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  No KYC verification history found.
                </Typography>
              </Box>
            ) : (
              <CustomerTableWithoutFilter
                data={kycHistory}
                columns={[
                  {
                    accessorKey: 'createdAt',
                    header: 'Attempt Date',
                    cell: ({ cell }) => {
                      return dayjs(cell.row.original.createdAt).format('DD/MM/YYYY HH:mm');
                    }
                  },
                  {
                    accessorKey: 'status',
                    header: 'Status',
                    cell: ({ cell }) => {
                      const status = cell.row.original.status;
                      let color = 'text.secondary';

                      if (status === 'Approved') {
                        color = 'success.main';
                      } else if (status === 'Rejected') {
                        color = 'error.main';
                      } else if (status === 'Pending') {
                        color = 'warning.main';
                      } else if (status === 'In Review') {
                        color = 'info.main';
                      }

                      return (
                        <Typography variant="body2" sx={{ color, fontWeight: 'bold' }}>
                          {status}
                        </Typography>
                      );
                    }
                  },
                  {
                    accessorKey: 'updatedAt',
                    header: 'Approved/Rejected On',
                    cell: ({ cell }) => {
                      const status = cell.row.original.status;
                      const updatedAt = cell.row.original.updatedAt;

                      if ((status === 'Approved' || status === 'Rejected') && updatedAt) {
                        return <Typography variant="body2">{dayjs(updatedAt).format('DD/MM/YYYY HH:mm')}</Typography>;
                      }
                      return '-';
                    }
                  },
                  {
                    accessorKey: 'rejectedReason',
                    header: 'Rejection Reason',
                    cell: ({ cell }) => {
                      const status = cell.row.original.status;
                      const rejectedReason = cell.row.original.rejectedReason;

                      if (status === 'Rejected' && rejectedReason) {
                        return (
                          <Typography
                            variant="body2"
                            sx={{
                              color: 'error.dark',
                              fontStyle: 'italic',
                              whiteSpace: 'pre-line'
                            }}
                          >
                            {rejectedReason}
                          </Typography>
                        );
                      }
                      return '-';
                    }
                  },
                  {
                    accessorKey: 'actions',
                    header: 'Actions',
                    cell: ({ cell }) => {
                      const status = cell.row.original.status;
                      const id = cell.row.original.id;

                      if (status === 'In Review') {
                        return (
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Approve">
                              <IconButton color="success" onClick={() => handleKycStatusUpdate(id, 'Approved')}>
                                <CheckCircleOutlined />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Reject">
                              <IconButton color="error" onClick={() => handleKycStatusUpdate(id, 'Rejected')}>
                                <CloseCircleOutlined />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        );
                      }
                      return '-';
                    }
                  }
                ]}
                category="KYC Verification"
              />
            )}
          </Grid>

          <Grid container>
            {/* Comments Section */}
            <Grid item xs={12} md={6} padding={2}>
              <Typography variant="subtitle1">Add Comments / Notify NGO</Typography>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                      <Editor
                        editorState={commentseditorState}
                        toolbarClassName="toolbarClassName"
                        wrapperClassName="wrapperClassName"
                        editorClassName="editorClassName"
                        toolbar={{
                          options: ['inline', 'fontSize', 'link'],
                          fontSize: {
                            options: [8, 10, 12, 14, 16]
                          },
                          inline: {
                            inDropdown: false,
                            className: undefined,
                            component: undefined,
                            dropdownClassName: undefined,
                            options: ['bold', 'italic', 'underline', 'strikethrough']
                          }
                        }}
                        onEditorStateChange={setCommentsEditorState}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    {commentEditing ? (
                      <div style={{ display: 'flex', justifyContent: 'end' }}>
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                          disabled={loading}
                          startIcon={loading && <CircularProgress size={20} color="inherit" />}
                        >
                          {loading ? 'Sending...' : 'Submit'}
                        </Button>
                        {/* <Button type="button" variant="outlined" color="secondary" onClick={handleCancelEdit}>
                          Cancel
                        </Button> */}
                      </div>
                    ) : (
                      <Button type="button" variant="contained" color="primary" onClick={handleEditClick}>
                        Edit
                      </Button>
                    )}
                  </Grid>
                </Grid>
              </form>
              <Box sx={{ marginTop: 2 }}>
                <FeedsCard userType="DR" comments={comments} title={'Comments'} />
              </Box>
            </Grid>

            {/* How-To Section */}
            <Grid item xs={12} md={6} padding={2}>
              <Typography variant="subtitle1">Add Note</Typography>
              <form onSubmit={handleJournalSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box style={{ marginTop: '16px' }} border={'1px solid #ccc'} padding={2} minHeight={250}>
                      <Editor
                        editorState={journalseditorState}
                        toolbarClassName="toolbarClassName"
                        wrapperClassName="wrapperClassName"
                        editorClassName="editorClassName"
                        toolbar={{
                          options: ['inline', 'fontSize', 'link'],
                          inline: {
                            inDropdown: false,
                            className: undefined,
                            component: undefined,
                            dropdownClassName: undefined,
                            options: ['bold', 'italic', 'underline', 'strikethrough']
                          },
                          fontSize: {
                            options: [8, 10, 12, 14, 16]
                          }
                        }}
                        onEditorStateChange={setJournalsEditorState}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    {journalEditing ? (
                      <div style={{ display: 'flex', justifyContent: 'end' }}>
                        <Button type="submit" variant="contained" color="primary">
                          Submit
                        </Button>
                      </div>
                    ) : (
                      <Button type="button" variant="contained" color="primary" onClick={handleJournalEditClick}>
                        Edit
                      </Button>
                    )}
                  </Grid>
                </Grid>
              </form>
              <Box sx={{ marginTop: 2 }}>
                <FeedsCard comments={journals} title={'Journals'} userType={'DR'} />
              </Box>
            </Grid>
            <Grid container sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', gap: 2, marginTop: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() =>
                  newNgoId ? navigate(`/masters/ngos/edit/socials/${newNgoId}`) : navigate('/apps/profiles/account/documents')
                }
              >
                Back
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>

      {/* KYC Rejection Dialog */}
      <Dialog
        open={openRejectionDialog}
        onClose={(_, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            setOpenRejectionDialog(false);
          }
        }}
        maxWidth="50%"
        PaperProps={{
          sx: {
            top: 0,
            position: 'absolute',
            borderRadius: '16px',
            width: '100%',
            maxWidth: '600px'
          }
        }}
      >
        <DialogTitle>Reject KYC Verification</DialogTitle>
        <DialogContent>
          <TextField
            label="Reason for Rejection"
            fullWidth
            multiline
            rows={3}
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            sx={{ marginTop: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenRejectionDialog(false)} color="error" disabled={isRejecting}>
            Cancel
          </Button>
          <Button onClick={handleKycReject} variant="contained" color="primary" disabled={isRejecting}>
            {isRejecting ? <CircularProgress size={24} /> : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* KYC Approval Confirmation Dialog */}
      <Dialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        maxWidth="sm"
        PaperProps={{
          sx: {
            borderRadius: '16px'
          }
        }}
      >
        <DialogTitle>Confirm KYC Approval</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to approve this KYC verification? This action cannot be undone.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenConfirmDialog(false)} color="error" disabled={isApproving}>
            Cancel
          </Button>
          <Button onClick={handleKycApprove} variant="contained" color="primary" disabled={isApproving}>
            {isApproving ? <CircularProgress size={24} /> : 'Confirm Approval'}
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer autoClose={6000} />
    </Card>
  );
}
