import axiosServices from 'utils/axios_node';

const API_BASE_URL = `${import.meta.env.VITE_APP_APPLICATION_API_URL}/api`;

/**
 * Get comprehensive campaign statistics for all campaigns of a specific NGO
 * @param {number} ngoId - The ID of the NGO
 * @returns {Promise} API response with NGO campaign statistics
 */
export const getNgoComprehensiveStats = async (ngoId) => {
  const url = ngoId
    ? `${API_BASE_URL}/campaigns/stats/ngoComprehensiveStats?ngoId=${ngoId}`
    : `${API_BASE_URL}/campaigns/stats/ngoComprehensiveStats`;

  const response = await axiosServices.get(url);
  return response?.data;
};

/**
 * Get detailed statistics for a single campaign
 * @param {number} campaignId - The ID of the campaign
 * @returns {Promise} API response with single campaign statistics
 */
export const getSingleCampaignStats = async (campaignId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns/stats/singleCampaignStats?campaignId=${campaignId}`);
  return response?.data;
};

/**
 * Get comprehensive event statistics for all events of a specific NGO
 * @param {number} ngoId - The ID of the NGO
 * @returns {Promise} API response with NGO event statistics
 */
export const getNgoEventStats = async (ngoId) => {
  const url = ngoId ? `${API_BASE_URL}/campaigns/stats/ngoEventStats?ngoId=${ngoId}` : `${API_BASE_URL}/campaigns/stats/ngoEventStats`;

  const response = await axiosServices.get(url);
  return response?.data;
};

/**
 * Get detailed statistics for a single event
 * @param {number} eventId - The ID of the event
 * @returns {Promise} API response with single event statistics
 */
export const getSingleEventStats = async (eventId) => {
  const response = await axiosServices.get(`${API_BASE_URL}/campaigns/stats/singleEventStats?eventId=${eventId}`);
  return response?.data;
};
