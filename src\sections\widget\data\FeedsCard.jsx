// import { Link as RouterLink } from 'react-router-dom';

// // material-ui
// import Grid from '@mui/material/Grid';
// import Link from '@mui/material/Link';
// import Box from '@mui/material/Box';
// import CardContent from '@mui/material/CardContent';
// import Typography from '@mui/material/Typography';

// // project imports
// import MainCard from 'components/MainCard';
// import Avatar from 'components/@extended/Avatar';

// // assets
// import MessageFilled from '@ant-design/icons/MessageFilled';
// import ShoppingFilled from '@ant-design/icons/ShoppingFilled';
// import FileTextFilled from '@ant-design/icons/FileTextFilled';

// // ==============================|| DATA WIDGET - FEEDS ||============================== //

// export default function FeedsCard() {
//   return (
//     <MainCard
//       title="Feeds"
//       content={false}
//       secondary={
//         <Link component={RouterLink} to="#" color="primary">
//           View all
//         </Link>
//       }
//     >
//       <CardContent>
//         <Grid container spacing={3}>
//           <Grid item xs={12}>
//             <Grid container spacing={2} alignItems="center" justifyContent="center">
//               <Grid item>
//                 <Box sx={{ position: 'relative' }}>
//                   <Avatar color="primary" type="filled" size="sm">
//                     <MessageFilled />
//                   </Avatar>
//                 </Box>
//               </Grid>
//               <Grid item xs zeroMinWidth>
//                 <Grid container spacing={1}>
//                   <Grid item xs zeroMinWidth>
//                     <Typography variant="body2">You have 3 pending tasks.</Typography>
//                   </Grid>
//                   <Grid item>
//                     <Typography variant="caption" color="secondary">
//                       just now
//                     </Typography>
//                   </Grid>
//                 </Grid>
//               </Grid>
//             </Grid>
//           </Grid>
//           <Grid item xs={12}>
//             <Grid container spacing={2} alignItems="center" justifyContent="center">
//               <Grid item>
//                 <Box sx={{ position: 'relative' }}>
//                   <Avatar color="error" type="filled" size="sm">
//                     <ShoppingFilled />
//                   </Avatar>
//                 </Box>
//               </Grid>
//               <Grid item xs zeroMinWidth>
//                 <Grid container spacing={1}>
//                   <Grid item xs zeroMinWidth>
//                     <Typography variant="body2">New order received</Typography>
//                   </Grid>
//                   <Grid item>
//                     <Typography variant="caption" color="secondary">
//                       1 day ago
//                     </Typography>
//                   </Grid>
//                 </Grid>
//               </Grid>
//             </Grid>
//           </Grid>
//           <Grid item xs={12}>
//             <Grid container spacing={2} alignItems="center" justifyContent="center">
//               <Grid item>
//                 <Box sx={{ position: 'relative' }}>
//                   <Avatar color="success" type="filled" size="sm">
//                     <FileTextFilled />
//                   </Avatar>
//                 </Box>
//               </Grid>
//               <Grid item xs zeroMinWidth>
//                 <Grid container spacing={1}>
//                   <Grid item xs zeroMinWidth>
//                     <Typography variant="body2">You have 3 pending tasks.</Typography>
//                   </Grid>
//                   <Grid item>
//                     <Typography variant="caption" color="secondary">
//                       3 week ago
//                     </Typography>
//                   </Grid>
//                 </Grid>
//               </Grid>
//             </Grid>
//           </Grid>
//           <Grid item xs={12}>
//             <Grid container spacing={2} alignItems="center" justifyContent="center">
//               <Grid item>
//                 <Box sx={{ position: 'relative' }}>
//                   <Avatar color="primary" type="filled" size="sm">
//                     <MessageFilled />
//                   </Avatar>
//                 </Box>
//               </Grid>
//               <Grid item xs zeroMinWidth>
//                 <Grid container spacing={1}>
//                   <Grid item xs zeroMinWidth>
//                     <Typography variant="body2">New order received</Typography>
//                   </Grid>
//                   <Grid item>
//                     <Typography variant="caption" color="secondary">
//                       around month
//                     </Typography>
//                   </Grid>
//                 </Grid>
//               </Grid>
//             </Grid>
//           </Grid>
//           <Grid item xs={12}>
//             <Grid container spacing={2} alignItems="center" justifyContent="center">
//               <Grid item>
//                 <Box sx={{ position: 'relative' }}>
//                   <Avatar color="warning" type="filled" size="sm">
//                     <ShoppingFilled />
//                   </Avatar>
//                 </Box>
//               </Grid>
//               <Grid item xs zeroMinWidth>
//                 <Grid container spacing={1}>
//                   <Grid item xs zeroMinWidth>
//                     <Typography variant="body2">Order cancelled</Typography>
//                   </Grid>
//                   <Grid item>
//                     <Typography variant="caption" color="secondary">
//                       2 month ago
//                     </Typography>
//                   </Grid>
//                 </Grid>
//               </Grid>
//             </Grid>
//           </Grid>
//         </Grid>
//       </CardContent>
//     </MainCard>
//   );
// }

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { Link as RouterLink } from 'react-router-dom';

// material-ui
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Box from '@mui/material/Box';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';

// project imports
import MainCard from 'components/MainCard';
import Avatar from 'components/@extended/Avatar';

// assets
import MessageFilled from '@ant-design/icons/MessageFilled';
import ShoppingFilled from '@ant-design/icons/ShoppingFilled';
import FileTextFilled from '@ant-design/icons/FileTextFilled';
import dayjs from 'dayjs';
import useAuth from 'hooks/useAuth';
import { getData } from 'api/map.service';
import { CardHeader } from '@mui/material';
import { getCommentText } from 'utils/permissionUtils';

// Component
export default function FeedsCard({ comments, title, userType }) {
  return (
    // <MainCard title={title} content={false}>
    <>
      <CardHeader title={title} />
      <Box sx={{ maxHeight: 800, overflowY: 'auto', paddingRight: 1 }}>
        {/* <CardContent> */}
        <Grid container spacing={3}>
          {comments?.length > 0 ? (
            comments.map((comment, index) => (
              <Grid item xs={12} key={index}>
                <Grid container spacing={2} alignItems="center" justifyContent="center">
                  <Grid item>
                    <Box sx={{ position: 'relative' }}>
                      <Avatar color={index % 2 === 0 ? 'primary' : 'success'} size="md">
                        {index % 2 === 0 ? <MessageFilled /> : <FileTextFilled />}
                      </Avatar>
                    </Box>
                  </Grid>
                  <Grid item xs zeroMinWidth>
                    <Grid container spacing={1}>
                      <Grid item xs zeroMinWidth>
                        <Typography
                          variant="body1"
                          component="div"
                          dangerouslySetInnerHTML={{ __html: getCommentText(comment.description) }}
                        />
                        <Typography variant="caption" color="secondary">
                          {`${dayjs(comment.createdAt).format('MMMM D, YYYY h:mm A')}`}
                        </Typography>
                      </Grid>
                      {/* <Grid item>
                        <Typography variant="caption" color="secondary">
                          {`At: ${dayjs(comment.createdAt).format('MMMM D, YYYY h:mm A')}`}
                        </Typography>
                      </Grid> */}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            ))
          ) : (
            <Box item xs zeroMinWidth>
              <Grid container spacing={1}>
                <Grid item xs zeroMinWidth>
                  {userType && userType === 'DR' ? (
                    <Typography variant="body1" style={{ marginLeft: '2.5rem', marginTop: '1.5rem' }}>
                      {title === 'Journals' ? 'No notes available.' : 'No comments available.'}
                    </Typography>
                  ) : (
                    <Typography variant="body1" style={{ marginLeft: '1rem' }}>
                      {title === 'Journals'
                        ? 'You did not add any notes.'
                        : 'There are currently no comments / notifications. Please check back later for updates!'}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </Box>
          )}
        </Grid>
      </Box>
      {/* </CardContent> */}
    </>
    // </MainCard>
  );
}
