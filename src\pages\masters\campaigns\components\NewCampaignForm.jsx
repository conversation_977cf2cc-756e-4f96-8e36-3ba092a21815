import React, { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  InputAdornment,
  Button,
  MenuItem,
  Typography,
  CircularProgress,
  Autocomplete,
  IconButton,
  CardMedia,
  Tooltip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox
} from '@mui/material';
import { Formik } from 'formik';
import * as yup from 'yup';
import { toast, ToastContainer } from 'react-toastify';
import { getAllCategories } from 'api/categories.service';
import { useNavigate, useParams } from 'react-router-dom';
import { addCampaign, getCampaignById, getNGOSBySearchTerm, patchCampaign, updateCampaign } from 'api/campaigns.service';
import useAuth from 'hooks/useAuth';
import { MinusCircleFilled, MinusCircleOutlined, PlusCircleFilled, SearchOutlined } from '@ant-design/icons';
import { values } from 'lodash';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { getDocumentsMasterList, getNGODocumentsList } from 'pages/masters/apis/documents.service';
import { setUserAgent } from 'react-device-detect';
import {
  addCampaignImageService,
  getCampaignImages,
  updateCampaignImageService,
  updateCampaignPromoImageService
} from 'pages/masters/components/images.service';
import MainCard from 'components/MainCard';
import { getNgoById } from 'api/ngos.service';
import { LocalizationProvider, MobileDatePicker, MobileTimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { useAddressHandler } from 'utils/googleAddressHandler';
import { useJsApiLoader } from '@react-google-maps/api';
import AddressDialog from './AddressDialog';
import { calculateProfileCompletion, formatIndianNumber, hasValidPriorities, sortByPriority } from 'utils/permissionUtils';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const validationSchema = yup.object({
  name: yup.string().required('Campaign title is required'),
  category_id: yup.string().required('Please select a category'),
  description: yup.string().required('Description is required'),
  fund_raising_target: yup
    .number()
    .typeError('Goal must be a number')
    .required('Goal amount is required')
    .min(1, 'Goal must be at least ₹1'),
  volunteers_required: yup.string().required('Required'),
  campaign_end_date: yup.date().required('Campaign End Date is required'),
  status: yup.string().required('Please select a Status')
});

export default function CampaignForm() {
  const { user } = useAuth();
  const MAX_LENGTH = 500;
  const [categories, setCategories] = useState([]);
  const [categoryImages, setCategoryImages] = useState([]);
  const [promoImages, setPromoImages] = useState([]);
  const [allImages, setAllImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [ngoLoading, setNgoLoading] = useState(false);
  const [ngoList, setNgoList] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [ratio, setRatio] = useState('');
  const [selectedNgo, setSelectedNgo] = useState(null);
  const [eventFormat, setEventFormat] = useState('physical');
  const [volunteerType, setVolunteerType] = useState('self');
  const [fullDay, setFullDay] = useState('no');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);

  //addressd related states
  const GOOGLE_MAPS_API_KEY = 'AIzaSyDAlmZjT27PfFPOFsUVixpv6jPPtwkRVcs';
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY
  });

  const [isPastCampaign, setIsPastCampaign] = useState(false);
  const [addressInformation, setAddressInformation] = useState({
    pincode: '',
    country: '',
    state: '',
    place_name: '',
    current_address: '',
    latitude: null,
    longitude: null
  });

  const {
    isLoadingLocate,
    error,
    suggestions,
    isLoadingSuggestion,
    isDialogOpen,
    markerPosition,
    isPincodeLoading,
    fetchLocationDetailsFromPincode,
    handleSearchSubmit,
    handleMarkerDragEnd,
    handleSuggestionClick,
    setIsDialogOpen
  } = useAddressHandler(setAddressInformation);

  const [initialValues, setInitialValues] = useState({
    name: '',
    category_id: null,
    description: '',
    fund_raising_target: null,
    campaign_start_date: dayjs().format('YYYY-MM-DD'),
    campaign_end_date: null,
    volunteers_required: 'no',
    number_of_volunteers: null,
    status: 'Active'
  });

  const { campaignId } = useParams();
  const navigate = useNavigate();

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) {
      toast.warn('Please enter at least 4 characters to search.');
      return;
    }
    setNgoLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setNgoLoading(false);
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        let eventData;
        const categoriesResponse = await getAllCategories();
        setCategories(categoriesResponse);

        if (campaignId) {
          let campaignData = await getCampaignById(campaignId);
          if (campaignData?.eventId) {
            eventData = await getCampaignById(campaignData.eventId);

            campaignData = {
              ...campaignData,
              event_type: eventData?.event_type,
              volunteer_type: eventData?.volunteer_type,
              event_date: eventData?.event_date,
              event_start_time: eventData?.event_start_time,
              event_end_time: eventData?.event_end_time,
              pincode: eventData?.pincode,
              place_name: eventData?.place_name,
              state: eventData?.state,
              country: eventData?.country,
              current_address: eventData?.current_address,
              fullday_event: eventData?.fullday_event,
              meeting_link: eventData?.meeting_link,
              amount_per_person: eventData?.amount_per_person
            };
          }

          const campaignImages = await getCampaignImages(campaignId);

          console.log('campaignImages', campaignImages);

          const promoImagesArray = campaignImages?.filter((item) => item.type === 'promo');

          console.log('promoImagesArray', promoImagesArray);

          const categoryImagesArray = campaignImages?.filter((item) => item.type === 'categories');

          console.log('categoryImagesArray', categoryImagesArray);

          console.log('campaignData', campaignData);

          if (campaignData) {
            setInitialValues({
              name: campaignData.name || '',
              category_id: campaignData.category_id || '',
              description: campaignData.description || '',
              fund_raising_target: campaignData.fund_raising_target || '',
              ratio: campaignData.ratio || '',
              status: campaignData?.status || null,
              ngo_id: campaignData.ngo_id || null,
              campaign_start_date: dayjs().format('YYYY-MM-DD'),
              campaign_end_date: campaignData.campaign_end_date || null,
              volunteers_required: campaignData.volunteers_required || '',
              number_of_volunteers: campaignData.number_of_volunteers || null,
              event_type: eventData?.event_type || '',
              volunteer_type: eventData?.volunteer_type || '',
              event_date: dayjs(eventData?.event_date) || null,
              event_start_time: eventData?.event_start_time ? dayjs(`2000-01-01T${eventData?.event_start_time}`) : dayjs(),
              event_end_time: eventData?.event_end_time ? dayjs(`2000-01-01T${eventData?.event_end_time}`) : dayjs(),
              pincode: eventData?.pincode || null,
              place_name: eventData?.place_name || null,
              state: eventData?.state || null,
              country: eventData?.country || null,
              current_address: eventData?.current_address || null,
              fullday_event: eventData?.fullday_event || null,
              meeting_link: eventData?.meeting_link || null,
              amount_per_person: eventData?.amount_per_person || null
            });
            if (eventData?.event_type) {
              setEventFormat(eventData.event_type);
            }
            if (eventData?.volunteer_type) {
              setVolunteerType(eventData.volunteer_type);
            }
            if (eventData?.fullday_event) {
              setFullDay(eventData.fullday_event);
            }

            console.log('images updated', campaignData?.imagesUpdated);

            if (!campaignData?.imagesUpdated) {
              const selectedCat = categoriesResponse.find((cat) => cat.id == campaignData.category_id);

              console.log('selectedCat', selectedCat);

              const userDocuments = await getNGODocumentsList(user?.ngo_id ?? campaignData.ngo_id);

              const promoDocs = userDocuments
                ?.filter((doc) => ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name))
                .map((img) => ({ ...img, markedForDeletion: 'no' }));

              const categoryDocs = (selectedCat?.images || []).map((img) => ({
                ...img,
                markedForDeletion: 'no'
              }));

              console.log('promoDocs', promoDocs);
              console.log('categoryDocs', categoryDocs);
              console.log('allImages', [...promoDocs, ...categoryDocs]);

              setPromoImages(promoDocs);
              setCategoryImages(categoryDocs);
              setAllImages([...promoDocs, ...categoryDocs]);
            } else {
              const promoCategoryImages = [...categoryImagesArray, ...promoImagesArray];
              const updatedpromoCategoryImages = hasValidPriorities(promoCategoryImages)
                ? sortByPriority(promoCategoryImages)
                : promoCategoryImages;

              console.log('updatedpromoCategoryImages', updatedpromoCategoryImages);

              setCategoryImages(categoryImagesArray);
              setPromoImages(promoImagesArray);
              setAllImages(updatedpromoCategoryImages);
            }
          }
          setRatio(campaignData?.ratio);
        } else {
          const userDocuments = await getNGODocumentsList(user?.ngo_id);

          const promoDocs = userDocuments?.filter((doc) =>
            ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name)
          );

          setPromoImages(promoDocs);
        }
      } catch (error) {
        toast.error('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campaignId, categories.length]);
  useEffect(() => {
    const fetchNgo = async () => {
      if (user?.ngo_id) {
        const ngo = await getNgoById(user.ngo_id);
        setSelectedNgo(ngo);
      }
    };

    fetchNgo();
  }, [user?.ngo_id]);

  useEffect(() => {
    const fetchPromoImages = async () => {
      if (!selectedNgo?.id) return;
      const userDocuments = await getNGODocumentsList(selectedNgo?.id);

      const promoDocs = userDocuments?.filter((doc) =>
        ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name)
      );

      setPromoImages(promoDocs);
      setCategoryImages(selectedCategory?.images);
    };
    fetchPromoImages();
  }, [selectedNgo?.id, selectedCategory?.id]);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const reordered = Array.from(allImages);
    const [removed] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, removed);

    // Assign new priority based on new order
    const reorderedWithPriority = reordered.map((img, index) => ({
      ...img,
      priority: index + 1 // assuming priority starts at 1
    }));

    const newPromo = reorderedWithPriority.filter((img) => img?.documentId);
    const newCat = reorderedWithPriority.filter((img) => !img?.documentId);

    setAllImages(reorderedWithPriority);
    setPromoImages(newPromo);
    setCategoryImages(newCat);
  };

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngoRecords = await getNgoById(user?.ngo_id);
          const profilepercentage = await calculateProfileCompletion(ngoRecords);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(100);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setIsSubmitting(true);

    try {
      if (campaignId) {
        if (!values.ngo_id) {
          toast.error('Please select a ngo');
          return;
        }
        let campaignData = await getCampaignById(campaignId);

        if (campaignData?.eventId) {
          const eventPayload = {
            name: values.name,
            fullday_event: fullDay,
            event_type: eventFormat,
            volunteer_type: volunteerType,
            campaign_start_date: values.campaign_start_date,
            campaign_end_date: values.campaign_end_date,
            category_id: values.category_id,
            fileName:
              allImages.length > 0
                ? allImages[0].type === 'categories'
                  ? `fetchCategoryImages/${allImages[0].fileName}`
                  : `fetchNGODocumentsPDF/${allImages[0].fileName}`
                : null,
            description: values.description,
            no_of_volunteers: values.number_of_volunteers,
            event_date: values.event_date ? dayjs(values.event_date).format('YYYY-MM-DD') : null,
            event_end_time: values.event_end_time ? dayjs(values.event_end_time).format('HH:mm:ss') : null,
            event_start_time: values.event_start_time ? dayjs(values.event_start_time).format('HH:mm:ss') : null,
            ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
            ratio: ratio,
            ...addressInformation,
            meeting_link: values.meeting_link || null,
            amount_per_person: values.amount_per_person || null,
            status: values.status || null,
            ...(values.status === 'Inactive' && {
              inactivatedBy: user?.id || null,
              inactivatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
            })
          };

          await updateCampaign(campaignData.eventId, eventPayload);
        }

        await updateCampaign(campaignId, {
          name: values.name,
          category_id: values.category_id,
          description: values.description,
          no_of_volunteers: values.number_of_volunteers,
          fund_raising_target: values.fund_raising_target,
          campaign_start_date: values.campaign_start_date,
          campaign_end_date: values.campaign_end_date,
          volunteers_required: values.volunteers_required,
          number_of_volunteers: values.number_of_volunteers,
          fileName:
            allImages.length > 0
              ? allImages[0].type === 'categories'
                ? `fetchCategoryImages/${allImages[0].fileName}`
                : `fetchNGODocumentsPDF/${allImages[0].fileName}`
              : null,
          ratio: ratio,
          ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
          status: values.status || null,
          ...(values.status === 'Inactive' && {
            inactivatedBy: user?.id || null,
            inactivatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss')
          })
        });

        const allImagesArray = [...categoryImages, ...promoImages];
        await updateCampaignPromoImageService(campaignId, allImagesArray);
        await patchCampaign(campaignId, { imagesUpdated: 'yes' });

        toast.success('Campaign updated successfully!');
      } else {
        if (values.volunteers_required == 'yes') {
          if (eventFormat === 'physical' && (!addressInformation.latitude || !addressInformation.longitude)) {
            toast.error('Please pin your address on the map, locate it, and confirm before submitting.');
            return;
          }
          const eventPayload = {
            name: values.name,
            fullday_event: fullDay,
            event_type: eventFormat,
            volunteer_type: volunteerType,
            status: 'Active',
            fileName:
              allImages.length > 0
                ? allImages[0].type === 'categories'
                  ? `fetchCategoryImages/${allImages[0].fileName}`
                  : `fetchNGODocumentsPDF/${allImages[0].fileName}`
                : promoImages.length > 0
                  ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
                  : '',
            category_id: values.category_id,
            description: values.description,
            no_of_volunteers: values.number_of_volunteers,
            event_date: values.campaign_start_date,
            campaign_start_date: values.campaign_start_date,
            campaign_end_date: values.campaign_end_date,
            event_end_time: values.event_end_time ? dayjs(values.event_end_time).format('HH:mm:ss') : null,
            event_start_time: values.event_start_time ? dayjs(values.event_start_time).format('HH:mm:ss') : null,
            ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
            ratio: ratio,
            ...addressInformation,
            current_address: values?.current_address
          };
          const createdEventResponse = await addCampaign(eventPayload);
          if (createdEventResponse.status) {
            await addCampaign({
              name: values.name,
              category_id: values.category_id,
              description: values.description,
              no_of_volunteers: values.number_of_volunteers,
              fund_raising_target: values.fund_raising_target,
              campaign_start_date: values.campaign_start_date,
              campaign_end_date: values.campaign_end_date,
              volunteers_required: values.volunteers_required,
              number_of_volunteers: values.number_of_volunteers,
              status: 'Active',
              eventId: createdEventResponse?.data?.id,
              ratio: ratio,
              fileName:
                allImages.length > 0
                  ? allImages[0].type === 'categories'
                    ? `fetchCategoryImages/${allImages[0].fileName}`
                    : `fetchNGODocumentsPDF/${allImages[0].fileName}`
                  : promoImages.length > 0
                    ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
                    : '',
              latitude: selectedNgo?.latitude,
              longitude: selectedNgo?.longitude,
              ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id
            });
            toast.success('Campaign and its corresponding event added successfully!');
          }
        } else {
          await addCampaign({
            name: values.name,
            category_id: values.category_id,
            description: values.description,
            number_of_volunteers: values.number_of_volunteers,
            fileName:
              allImages.length > 0
                ? allImages[0].type === 'categories'
                  ? `fetchCategoryImages/${allImages[0].fileName}`
                  : `fetchNGODocumentsPDF/${allImages[0].fileName}`
                : promoImages.length > 0
                  ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
                  : '',
            fund_raising_target: values.fund_raising_target,
            campaign_start_date: values.campaign_start_date,
            campaign_end_date: values.campaign_end_date,
            latitude: selectedNgo?.latitude,
            longitude: selectedNgo?.longitude,
            status: 'Active',
            volunteers_required: values.volunteers_required,
            ratio: ratio,
            ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id
          });
          toast.success('Campaign added successfully!');
        }
      }

      navigate('/masters/campaigns');
    } catch (error) {
      toast.error(error?.message || 'Failed to save campaign');
    } finally {
      setSubmitting(false);
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  const formatIndianNumber = (value) => {
    if (!value) return '';
    const parts = value.toString().split('.');
    const intPart = parts[0];
    const decimalPart = parts[1] ? parts[1].slice(0, 2) : '';

    const x = intPart.replace(/\B(?=(\d{2})+(?!\d))/g, ',').replace(/^(\d+)(?=(\d{3})+(?!\d))/g, '$1,');
    return decimalPart ? `${x}.${decimalPart}` : x;
  };

  const toggleImageDeletion = (id, type) => {
    console.log('id', id, 'type', type);
    const updateImages = (images) =>
      images?.map((img) => (img.id === id ? { ...img, markedForDeletion: img.markedForDeletion === 'yes' ? 'no' : 'yes' } : img));

    if (type === 'promo') {
      setPromoImages((prev) => {
        const updated = updateImages(prev);
        console.log('prev', prev);
        console.log('updated', updated);
        updateImages(prev);
      });
    } else {
      setCategoryImages((prev) => {
        const updated = updateImages(prev);
        console.log('prev', prev);
        console.log('updated', updated);
        updateImages(prev);
      });
    }
  };

  console.log('promo', promoImages);
  console.log('category', categoryImages);
  return (
    <Grid container>
      <Grid item xs={12} sm={7}>
        {user?.ngo_id && selectedNgo && profileCompletePercentage < 100 && (
          <Grid item xs={12} sx={{ marginBottom: 2 }}>
            <ProfileCard ngoInfo={selectedNgo} profileCompletePercentage={profileCompletePercentage} />
          </Grid>
        )}
        <MainCard
          title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>{campaignId ? 'Edit Campaign' : 'Add Campaign'}</Typography>}
          subheader={
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              {user?.ngo_id && (
                <Typography variant="body1" color="text.secondary">
                  By {selectedNgo?.name}
                </Typography>
              )}
              {isPastCampaign && (
                <Typography variant="h5" color="primary">
                  You can't edit this campaign as it has already ended.
                </Typography>
              )}
            </Box>
          }
        >
          <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit} enableReinitialize>
            {({ values, handleChange, handleBlur, handleSubmit, errors, touched, setFieldValue }) => {
              useEffect(() => {
                const fetchNgo = async () => {
                  if (values.ngo_id && !ngoList.find((ngo) => ngo.id === values.ngo_id)) {
                    try {
                      const ngo = await getNgoById(values.ngo_id);
                      setSelectedNgo(ngo);
                    } catch (error) {
                      console.error(error);
                    }
                  }
                };
                fetchNgo();
              }, [values.ngo_id, ngoList, initialValues.ngo_id]);
              useEffect(() => {
                if (values.pincode && values.pincode.length === 6) {
                  fetchLocationDetailsFromPincode(values.pincode, setFieldValue);
                }
              }, [values.pincode]);

              useEffect(() => {
                if (values?.campaign_end_date) {
                  const isPast = dayjs(values.campaign_end_date).isBefore(dayjs(), 'day');
                  setIsPastCampaign(isPast);
                } else {
                  setIsPastCampaign(false); // Reset if no date
                }
              }, [values?.campaign_end_date]);
              return (
                <form onSubmit={handleSubmit} autoComplete="off">
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={12}>
                        <Grid container spacing={3} direction="column">
                          {!user?.ngo_id && (
                            <Grid item xs={12} sm={6}>
                              <Autocomplete
                                options={ngoList}
                                getOptionLabel={(option) => option.name}
                                value={ngoList.find((ngo) => ngo.id === values.ngo_id) || selectedNgo || null}
                                onInputChange={(event, newInputValue) => {
                                  setSearchTerm(newInputValue);
                                }}
                                onChange={(event, newValue) => {
                                  setSelectedNgo(newValue);
                                  setFieldValue('ngo_id', newValue?.id || null);
                                }}
                                loading={ngoLoading}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    required
                                    label="Search NGO"
                                    error={touched.ngo_id && Boolean(errors.ngo_id)}
                                    helperText={touched.ngo_id && errors.ngo_id}
                                    InputProps={{
                                      ...params.InputProps,
                                      endAdornment: (
                                        <>
                                          <InputAdornment position="end">
                                            {ngoLoading ? (
                                              <CircularProgress size={20} />
                                            ) : (
                                              <Button
                                                variant="contained"
                                                size="small"
                                                sx={{ minWidth: '60px', px: 2 }}
                                                onClick={() => fetchNgos(searchTerm)}
                                              >
                                                Search
                                              </Button>
                                            )}
                                          </InputAdornment>
                                          {params.InputProps.endAdornment}
                                        </>
                                      )
                                    }}
                                  />
                                )}
                              />
                            </Grid>
                          )}

                          <Grid item xs={12} sm={6}>
                            {' '}
                            <TextField
                              fullWidth
                              label="Campaign Title"
                              name="name"
                              value={values.name}
                              required
                              onChange={handleChange}
                              onBlur={handleBlur}
                              error={touched.name && Boolean(errors.name)}
                              helperText={touched.name && errors.name}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                              <Grid item xs={12} sm={6}>
                                <Autocomplete
                                  fullWidth
                                  options={categories}
                                  getOptionLabel={(option) => option.name || ''}
                                  value={categories.find((cat) => cat.id === values.category_id) || null}
                                  onChange={(event, newValue) => {
                                    const selectedId = newValue ? newValue.id : '';
                                    setSelectedCategory(newValue);
                                    setFieldValue('category_id', selectedId);

                                    if (newValue) {
                                      setRatio(newValue.ratio || '');
                                      setFieldValue('ratio', newValue.ratio || '');
                                      setCategoryImages(newValue.images || []);
                                    }
                                  }}
                                  onBlur={handleBlur}
                                  isOptionEqualToValue={(option, value) => option.id === value.id}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      label="Category"
                                      name="category_id"
                                      required
                                      error={touched.category_id && Boolean(errors.category_id)}
                                      helperText={touched.category_id && errors.category_id}
                                    />
                                  )}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  label="Goal Amount"
                                  name="fund_raising_target"
                                  required
                                  value={formatIndianNumber(values.fund_raising_target)}
                                  onChange={(e) => {
                                    let value = e.target.value.replace(/[^0-9.]/g, '');

                                    // Only allow one decimal point
                                    const parts = value.split('.');
                                    if (parts.length > 2) return;

                                    // Limit integer and decimal digits
                                    let integerPart = parts[0].slice(0, 10); // max 10 digits
                                    let decimalPart = parts[1]?.slice(0, 2) || '';

                                    let cleanedValue = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;

                                    setFieldValue('fund_raising_target', cleanedValue);
                                  }}
                                  onBlur={handleBlur}
                                  error={touched.fund_raising_target && Boolean(errors.fund_raising_target)}
                                  helperText={touched.fund_raising_target && errors.fund_raising_target}
                                  InputProps={{
                                    startAdornment: <InputAdornment position="start">₹</InputAdornment>
                                  }}
                                />
                                {values.fund_raising_target && ratio && (
                                  <Typography variant="body1" color={'primary'} sx={{ fontWeight: 'bold', mt: 0.5 }}>
                                    Estimated Lives Impacted:{' '}
                                    <strong>{Math.floor((values.fund_raising_target * parseFloat(ratio)) / 100)}</strong> people
                                  </Typography>
                                )}
                              </Grid>
                            </Grid>
                          </Grid>

                          {!user?.ngo_id && (
                            <Grid item xs={12} sm={6}>
                              <TextField
                                label="Ratio"
                                type="text"
                                inputProps={{
                                  inputMode: 'decimal',
                                  pattern: '[0-9]*[.]?[0-9]*',
                                  maxLength: 4
                                }}
                                value={values.ratio || ''}
                                onChange={(e) => {
                                  const newValue = e.target.value;
                                  setRatio(newValue); // Update local state
                                  setFieldValue('ratio', newValue); // Update formik values
                                }}
                                fullWidth
                              />
                            </Grid>
                          )}

                          <Grid item xs={12} sm={6}>
                            <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                              <Grid item xs={12} sm={6}>
                                <MobileDatePicker
                                  label="Campaign End Date"
                                  value={values.campaign_end_date ? dayjs(values.campaign_end_date) : null}
                                  onChange={(newValue) => {
                                    setFieldValue('campaign_end_date', dayjs(newValue).format('YYYY-MM-DD'));
                                  }}
                                  format="DD/MM/YYYY"
                                  minDate={dayjs(values.campaign_start_date)} // Optional: prevent earlier end date
                                  slotProps={{
                                    textField: {
                                      fullWidth: true,
                                      name: 'campaign_end_date',
                                      required: true,
                                      error: touched.campaign_end_date && Boolean(errors.campaign_end_date),
                                      helperText: touched.campaign_end_date && errors.campaign_end_date
                                    }
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  select
                                  fullWidth
                                  required
                                  name="status"
                                  label="Status"
                                  value={values.status}
                                  onChange={handleChange}
                                  error={touched.status && !!errors.status}
                                  helperText={touched.status && errors.status}
                                >
                                  <MenuItem value="Active">Active</MenuItem>
                                  <MenuItem value="Inactive">Inactive</MenuItem>
                                  <MenuItem value="Completed">Completed</MenuItem>
                                </TextField>
                              </Grid>
                            </Grid>
                          </Grid>

                          {/* <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Lives Impacted"
                          value={
                            values.fund_raising_target && ratio ? Math.floor((values.fund_raising_target * parseFloat(ratio)) / 100) : ''
                          }
                          disabled
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                <InputAdornment position="end">
                                  {ngoLoading ? (
                                    <CircularProgress size={20} />
                                  ) : (
                                    <Button
                                      variant="contained"
                                      size="small"
                                      sx={{ minWidth: '60px', px: 2 }}
                                      onClick={() => fetchNgos(searchTerm)}
                                    >
                                      Search
                                    </Button>
                                  )}
                                </InputAdornment>
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                        />
                      </Grid> */}

                          <Grid item xs={12} sm={6}>
                            {' '}
                            <TextField
                              fullWidth
                              multiline
                              rows={4}
                              label="Description"
                              required
                              name="description"
                              value={values.description}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              inputProps={{ maxLength: MAX_LENGTH }}
                              error={touched.description && Boolean(errors.description)}
                              helperText={touched.description && errors.description}
                            />
                            <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                              {values?.description?.length ? MAX_LENGTH - values?.description?.length : MAX_LENGTH} characters left
                            </Typography>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Typography variant="subtitle1" gutterBottom>
                              Volunteers Required? *
                            </Typography>
                            <RadioGroup row name="volunteers_required" value={values.volunteers_required} onChange={handleChange}>
                              <FormControlLabel value="yes" control={<Radio color="primary" />} label="Yes" />
                              <FormControlLabel value="no" control={<Radio color="primary" />} label="No" />
                            </RadioGroup>
                          </Grid>
                          {values.volunteers_required === 'yes' && (
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                required
                                label="Number of Volunteers"
                                name="number_of_volunteers"
                                inputProps={{ min: 1 }}
                                value={values.number_of_volunteers}
                                onChange={(e) => {
                                  const onlyNums = e.target.value.replace(/[^0-9]/g, '');
                                  handleChange({
                                    target: {
                                      name: 'number_of_volunteers',
                                      value: onlyNums
                                    }
                                  });
                                }}
                                onBlur={handleBlur}
                                disabled={values.volunteers_required == 'no'}
                                error={touched.number_of_volunteers && Boolean(errors.number_of_volunteers)}
                                helperText={touched.number_of_volunteers && errors.number_of_volunteers}
                              />
                            </Grid>
                          )}

                          {values.volunteers_required === 'yes' && (
                            <>
                              <Grid item xs={12} sm={6}>
                                <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                  <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle1" gutterBottom>
                                      Event Format *
                                    </Typography>
                                    <RadioGroup row value={eventFormat} onChange={(e) => setEventFormat(e.target.value)}>
                                      <FormControlLabel value="physical" control={<Radio color="primary" />} label="Physical" />
                                      <FormControlLabel value="virtual" control={<Radio color="primary" />} label="Virtual" />
                                    </RadioGroup>
                                  </Grid>

                                  <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle1" gutterBottom>
                                      Volunteer Type *
                                    </Typography>
                                    <RadioGroup row value={volunteerType} onChange={(e) => setVolunteerType(e.target.value)}>
                                      <FormControlLabel value="self" control={<Radio color="primary" />} label="Self (Free)" />
                                      <FormControlLabel value="paid" control={<Radio color="primary" />} label="Paid" />
                                    </RadioGroup>
                                  </Grid>
                                </Grid>
                              </Grid>
                              {volunteerType == 'paid' && (
                                <Grid item xs={12} sm={6}>
                                  <TextField
                                    name="amount_per_person"
                                    label="Amount per Person (₹)"
                                    required
                                    value={volunteerType === 'self' ? null : values.amount_per_person}
                                    onChange={(e) => {
                                      const val = e.target.value;
                                      if (/^\d*$/.test(val) && Number(val) >= 1) {
                                        setFieldValue('amount_per_person', val);
                                      }
                                    }}
                                    InputProps={{ inputProps: { min: 1 } }}
                                    disabled={volunteerType === 'self'}
                                    error={touched.amount_per_person && !!errors.amount_per_person}
                                    helperText={
                                      volunteerType === 'self'
                                        ? 'Free volunteer (₹0)'
                                        : touched.amount_per_person && errors.amount_per_person
                                    }
                                    fullWidth
                                  />
                                </Grid>
                              )}
                              {/* <Grid item xs={12} sm={6}>
                                <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                  <Grid item xs={12} sm={6}>
                                    <MobileDatePicker
                                      label="Event Date *"
                                      format="DD/MM/YYYY"
                                      minDate={dayjs()}
                                      maxDate={dayjs().add(102, 'day')}
                                      value={values.event_date}
                                      onChange={(value) => setFieldValue('event_date', value)}
                                      slotProps={{ textField: { fullWidth: true } }}
                                    />
                                  </Grid>

                                
                                </Grid>
                              </Grid> */}
                              <Grid item xs={12} sm={6}>
                                <FormControlLabel
                                  control={
                                    <Checkbox checked={fullDay === 'yes'} onChange={(e) => setFullDay(e.target.checked ? 'yes' : 'no')} />
                                  }
                                  label="Full Day Event *"
                                />
                              </Grid>
                              {fullDay == 'no' && (
                                <Grid item xs={12} sm={6}>
                                  <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                    <Grid item xs={12} sm={6}>
                                      <MobileTimePicker
                                        label="Start Time *"
                                        value={values.event_start_time}
                                        onChange={(value) => setFieldValue('event_start_time', value)}
                                        slotProps={{ textField: { fullWidth: true } }}
                                      />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                      <MobileTimePicker
                                        label="End Time *"
                                        value={values.event_end_time}
                                        onChange={(value) => setFieldValue('event_end_time', value)}
                                        slotProps={{ textField: { fullWidth: true } }}
                                      />
                                    </Grid>
                                  </Grid>
                                </Grid>
                              )}
                              {eventFormat === 'virtual' ? (
                                <Grid item xs={12} sm={6}>
                                  {' '}
                                  <TextField
                                    label="Meeting Link *"
                                    name="meeting_link"
                                    value={values.meeting_link}
                                    onChange={handleChange}
                                    required
                                    fullWidth
                                    error={touched.meeting_link && !!errors.meeting_link}
                                    helperText={touched.meeting_link && errors.meeting_link}
                                  />
                                </Grid>
                              ) : (
                                <>
                                  <Grid item xs={12} sm={6}>
                                    <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          required
                                          label="Pincode"
                                          name="pincode"
                                          value={values?.pincode || ''}
                                          // disabled={values.pincode}
                                          onChange={handleChange}
                                          sx={{ marginRight: 5 }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          required
                                          label="City"
                                          name="place_name"
                                          value={values.place_name || ''}
                                          // disabled={values.place_name}
                                          onChange={handleChange}
                                        />
                                      </Grid>

                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          required
                                          // disabled={values.state}
                                          label="State"
                                          name="state"
                                          value={values.state || ''}
                                          onChange={handleChange}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          required
                                          label="Country"
                                          name="country"
                                          value={values.country || ''}
                                          // disabled={values.country}
                                          onChange={handleChange}
                                        />
                                      </Grid>
                                    </Grid>
                                  </Grid>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      multiline
                                      rows={4}
                                      label="Address"
                                      required
                                      name="current_address"
                                      value={values?.current_address}
                                      onChange={(e) => {
                                        handleChange(e); // pass the event to Formik
                                        setAddressInformation((prev) => ({
                                          ...prev,
                                          current_address: e.target.value // safer than values?.current_address
                                        }));
                                      }}
                                      InputLabelProps={{ shrink: true }}
                                      // inputProps={{ maxLength: 300 }}
                                      InputProps={{
                                        endAdornment: (
                                          <InputAdornment position="end">
                                            <Button
                                              variant="contained"
                                              color="primary"
                                              onClick={() => handleSearchSubmit(values?.current_address, values?.pincode)}
                                              disabled={isLoadingLocate}
                                            >
                                              {isLoadingLocate ? <CircularProgress size={20} color="inherit" /> : 'Pin Your Address'}
                                            </Button>
                                          </InputAdornment>
                                        )
                                      }}
                                    />
                                  </Grid>
                                </>
                              )}{' '}
                            </>
                          )}

                          {(categoryImages?.length > 0 || promoImages?.length > 0) && campaignId && (
                            <Grid item xs={12}>
                              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                                Promo Images
                              </Typography>
                              <DragDropContext onDragEnd={handleDragEnd}>
                                <Droppable droppableId="images" direction="horizontal">
                                  {(provided) => (
                                    <Box
                                      ref={provided.innerRef}
                                      {...provided.droppableProps}
                                      sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}
                                    >
                                      {allImages.map((img, index) => (
                                        <Draggable draggableId={`${img.type}-${img.id}`} index={index} key={`${img.type}-${img.id}`}>
                                          {(provided) => (
                                            <Box
                                              ref={provided.innerRef}
                                              {...provided.draggableProps}
                                              {...provided.dragHandleProps}
                                              sx={{
                                                position: 'relative',
                                                boxShadow: 7,
                                                borderRadius: 2,
                                                overflow: 'hidden',
                                                width: 100,
                                                height: 80
                                              }}
                                            >
                                              <Tooltip title={img.markedForDeletion === 'yes' ? 'Add Image' : 'Remove Image'}>
                                                <IconButton
                                                  size="large"
                                                  onClick={() => toggleImageDeletion(img.id, img.type)}
                                                  sx={{
                                                    position: 'absolute',
                                                    top: -12,
                                                    right: -12,
                                                    color: img.markedForDeletion === 'yes' ? 'green' : 'red',
                                                    zIndex: 3,
                                                    '&:hover': {
                                                      backgroundColor: 'white',
                                                      color: img.markedForDeletion === 'yes' ? 'darkgreen' : 'darkred',
                                                      borderRadius: 10
                                                    }
                                                  }}
                                                >
                                                  {img.markedForDeletion === 'yes' ? <PlusCircleFilled /> : <MinusCircleFilled />}
                                                </IconButton>
                                              </Tooltip>
                                              <CardMedia
                                                component="img"
                                                image={`${BASE_URL}/${img.documentId || img.type == 'promo' ? 'fetchNGODocumentsPDF' : 'fetchCategoryImages'}/${img.fileName}`}
                                                alt={img.description || 'Image'}
                                                sx={{
                                                  width: '100%',
                                                  height: '100%',
                                                  objectFit: 'cover',
                                                  opacity: img.markedForDeletion === 'yes' ? 0.4 : 1,
                                                  filter: img.markedForDeletion === 'yes' ? 'grayscale(100%)' : 'none',
                                                  transition: '0.3s ease'
                                                }}
                                              />
                                            </Box>
                                          )}
                                        </Draggable>
                                      ))}
                                      {provided.placeholder}
                                    </Box>
                                  )}
                                </Droppable>
                              </DragDropContext>
                            </Grid>
                          )}
                        </Grid>
                      </Grid>

                      <Grid item xs={12}>
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                          disabled={(user?.ngo_id && profileCompletePercentage < 100) || isPastCampaign}
                        >
                          {isSubmitting ? (
                            campaignId ? (
                              <CircularProgress size={24} color="secondary" />
                            ) : (
                              <CircularProgress size={24} color="secondary" />
                            )
                          ) : campaignId ? (
                            'Update Campaign'
                          ) : (
                            'Add Campaign'
                          )}
                        </Button>
                      </Grid>
                    </Grid>
                  </LocalizationProvider>
                </form>
              );
            }}
          </Formik>

          <AddressDialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            isLoaded={isLoaded}
            markerPosition={markerPosition}
            onMarkerDragEnd={handleMarkerDragEnd}
            addressInformation={addressInformation}
            campaignData={values}
          />
          <ToastContainer />
        </MainCard>
      </Grid>
    </Grid>
  );
}
