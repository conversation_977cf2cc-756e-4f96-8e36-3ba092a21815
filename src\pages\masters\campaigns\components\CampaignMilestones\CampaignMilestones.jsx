import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  FormHelperText,
  Grid,
  TextField,
  Stack,
  Typography,
  DialogTitle
} from '@mui/material';
import { Formik } from 'formik';
import React from 'react';
import { FormattedMessage } from 'react-intl';
import { getConvertedFileName } from 'utils/permissionUtils';
import * as yup from 'yup';
import UploadSingleFile from 'components/third-party/dropzone/SingleFile';
import useAuth from 'hooks/useAuth';
import { DatePicker, LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

export default function CampaignMilestones({ isEditing, openDialog, setOpenDialog, campaign, handleMileStoneSubmit }) {
  const { user } = useAuth();
  return (
    <Dialog open={openDialog} onClose={() => setOpenDialog(false)} disableEscapeKeyDown={true}>
      <Formik
        initialValues={{ description: null, files: null }}
        onSubmit={async (values) => {
          // submit form
          let formData = new FormData();
          const convertedFileName = getConvertedFileName(values?.files[0]?.name);
          formData.append('fileName', `${convertedFileName}`);
          formData.append('file', values?.files[0], `${convertedFileName}`);
          formData.append('description', values?.description);
          formData.append('collection_date', values?.collection_date);
          formData.append('ngo_id', campaign?.ngo_id);
          formData.append('campaign_id', campaign.id);
          formData.append('portal_user_id', user?.id);
          await handleMileStoneSubmit(formData);
        }}
        validationSchema={yup.object().shape({
          collection_date: yup.date().required('Collection Date is required'),
          description: yup.string().required('Description is required'),
          files: yup.mixed().required('File is a required.')
        })}
      >
        {({ values, handleSubmit, handleChange, setFieldValue, touched, errors }) => (
          <form onSubmit={handleSubmit}>
            <DialogTitle>
              {isEditing ? (
                <FormattedMessage id="editMileStone" />
              ) : (
                <FormattedMessage id="addMileStone" values={{ campaignName: campaign?.name }} />
              )}
            </DialogTitle>
            <DialogContent style={{ padding: '1rem' }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Stack spacing={1}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <MobileDatePicker
                        autoFocus
                        label="Collection Date *"
                        value={values.collection_date}
                        format="DD/MM/YYYY"
                        maxDate={dayjs()}
                        onChange={(newValue) => setFieldValue('collection_date', newValue)}
                        error={touched.collection_date && Boolean(errors.collection_date)}
                        helperText={touched.collection_date && errors.collection_date}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            placeholder="Start Date"
                            margin="normal"
                            error={touched.collection_date && Boolean(errors.collection_date)}
                            helperText={touched.collection_date && errors.collection_date}
                          />
                        )}
                      />
                      {touched.collection_date && errors.collection_date && <FormHelperText error>{errors.collection_date}</FormHelperText>}
                    </LocalizationProvider>
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Stack spacing={1.5} alignItems="center">
                    <TextField
                      multiline
                      rows={4}
                      margin="dense"
                      label="Milestone Description"
                      type="text"
                      required
                      fullWidth
                      name="description"
                      variant="outlined"
                      value={values?.description || ''}
                      onChange={handleChange}
                    />
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Stack spacing={1.5} alignItems="center">
                    <UploadSingleFile setFieldValue={setFieldValue} file={values.files} error={touched.files && !!errors.files} />
                    <Stack spacing={0}>
                      <Typography align="center" variant="caption" color="secondary">
                        *.png, *.jpeg, *.jpg, *.gif
                      </Typography>
                    </Stack>
                  </Stack>
                  {touched.files && errors.files && (
                    <FormHelperText error id="standard-weight-helper-text-password-login">
                      {errors.files}
                    </FormHelperText>
                  )}
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Grid item xs={12}>
                <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
                  <Button
                    color="error"
                    onClick={() => {
                      setFieldValue('files', null);
                      setOpenDialog(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" variant="contained">
                    Submit
                  </Button>
                </Stack>
              </Grid>
              {/* <Button onClick={() => setOpenDialog(false)} color="primary">
                  Cancel
                </Button>
                <Button onClick={handleAddOrEdit} color="primary">
                  {isEditing ? 'Update' : 'Add'}
                </Button> */}
            </DialogActions>
          </form>
        )}
      </Formik>
    </Dialog>
  );
}
