import React, { useCallback, useEffect, useState } from 'react';
import {
  TextField,
  Grid,
  Button,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  Checkbox,
  FormControlLabel,
  Box,
  CircularProgress,
  Autocomplete,
  InputAdornment,
  CardMedia,
  IconButton,
  Paper,
  Radio,
  RadioGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip
} from '@mui/material';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { LocalizationProvider, MobileDatePicker, MobileTimePicker } from '@mui/x-date-pickers';
import { getAllCategories } from 'api/categories.service';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { addCampaign, getCampaignById, getNGOSBySearchTerm, patchCampaign, updateCampaign } from 'api/campaigns.service';
import { toast, ToastContainer } from 'react-toastify';

import { useParams, useNavigate } from 'react-router-dom';
import useAuth from 'hooks/useAuth';
import { BASE_URL } from 'sections/apps/profiles/profile.service';
import { MinusCircleFilled, PlusCircleFilled } from '@ant-design/icons';
import { getCampaignImages, updateCampaignPromoImageService } from 'pages/masters/components/images.service';
import { getNGODocumentsList } from 'pages/masters/apis/documents.service';
import MainCard from 'components/MainCard';
import { getNgoById } from 'api/ngos.service';
import axios from 'axios';
import LocationMap from 'components/LocationMap';
import { useJsApiLoader } from '@react-google-maps/api';
import { useAddressHandler } from 'utils/googleAddressHandler';
import AddressDialog from './AddressDialog';
import { calculateProfileCompletion, hasValidPriorities, sortByPriority } from 'utils/permissionUtils';
import ProfileCard from 'sections/apps/profiles/user/ProfileCard';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const EventForm = () => {
  const { user } = useAuth();
  const MAX_LENGTH = 500;
  const [eventFormat, setEventFormat] = useState('physical');
  const [fullDay, setFullDay] = useState('no');
  const [volunteerType, setVolunteerType] = useState('self');
  const [categories, setCategories] = useState([]);
  const [categoryImages, setCategoryImages] = useState([]);
  const [promoImages, setPromoImages] = useState([]);
  const [allImages, setAllImages] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [ratio, setRatio] = useState('');
  const [selectedNgo, setSelectedNgo] = useState(null);
  const [campaignData, setCampaignData] = useState(null);

  const [loading, setLoading] = useState(true);
  const [ngoLoading, setNgoLoading] = useState(false);
  const [ngoList, setNgoList] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [initialValues, setInitialValues] = useState(null);
  const { eventId } = useParams();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [profileCompletePercentage, setProfileCompletePercentage] = useState(0);
  const [ngoInfo, setNgoInfo] = useState(null);

  const GOOGLE_MAPS_API_KEY = 'AIzaSyDAlmZjT27PfFPOFsUVixpv6jPPtwkRVcs';
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_MAPS_API_KEY
  });

  const [addressInformation, setAddressInformation] = useState({
    pincode: '',
    country: '',
    state: '',
    place_name: '',
    current_address: '',
    latitude: null,
    longitude: null
  });

  const {
    isLoadingLocate,
    error,
    suggestions,
    isLoadingSuggestion,
    isDialogOpen,
    markerPosition,
    isPincodeLoading,
    fetchLocationDetailsFromPincode,
    handleSearchSubmit,
    handleMarkerDragEnd,
    handleSuggestionClick,
    setIsDialogOpen
  } = useAddressHandler(setAddressInformation);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await getAllCategories();
        setCategories(response);

        if (eventId) {
          const campaignData = await getCampaignById(eventId);
          setCampaignData(campaignData);
          const campaignImages = await getCampaignImages(eventId);
          setAddressInformation({
            pincode: campaignData.pincode || '',
            place_name: campaignData.place_name || '',
            country: campaignData.country || '',
            state: campaignData.state || '',
            current_address: campaignData.current_address || '',
            longitude: campaignData.longitude || '',
            latitude: campaignData.latitude || ''
          });
          const promoImagesArray = campaignImages?.filter((item) => item.type === 'promo');
          const categoryImagesArray = campaignImages?.filter((item) => item.type === 'categories');

          if (campaignData) {
            setEventFormat(campaignData.event_type || 'physical');
            setFullDay(campaignData.fullday_event || 'no');
            setVolunteerType(campaignData.volunteer_type || 'self');
            setInitialValues({
              name: campaignData.name || '',
              category_id: campaignData.category_id || null,
              description: campaignData.description || '',
              no_of_volunteers: campaignData.no_of_volunteers || null,
              event_date: dayjs(campaignData.event_date) || dayjs(),
              event_start_time: campaignData?.event_start_time ? dayjs(`2000-01-01T${campaignData?.event_start_time}`) : dayjs(),
              event_end_time: campaignData?.event_end_time ? dayjs(`2000-01-01T${campaignData?.event_end_time}`) : dayjs(),
              meeting_link: campaignData.meeting_link || '',
              address: campaignData.address || '',
              ratio: campaignData.ratio || '',
              place_name: campaignData.place_name || null,
              country: campaignData.country || null,
              state: campaignData.state || null,
              current_address: campaignData.current_address || null,
              pincode: campaignData.pincode || null,
              ngo_id: campaignData.ngo_id || null,
              amount_per_person: campaignData.amount_per_person || null
            });
            if (!campaignData?.imagesUpdated) {
              const selectedCategory = categories.find((cat) => cat.id == campaignData.category_id);

              const userDocuments = await getNGODocumentsList(user?.ngo_id ?? campaignData.ngo_id);

              const promoDocs = userDocuments
                ?.filter((doc) => ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name))
                .map((img) => ({ ...img, markedForDeletion: 'no' }));

              setPromoImages(promoDocs);

              if (selectedCategory) {
                const categoryDocs = (selectedCategory?.images || []).map((img) => ({
                  ...img,
                  markedForDeletion: 'no'
                }));
                setCategoryImages(categoryDocs);
                const all = [...(promoDocs || []), ...(categoryDocs || [])];

                setAllImages(all);
              }
            } else {
              const promoCategoryImages = [...categoryImagesArray, ...promoImagesArray];
              const updatedpromoCategoryImages = hasValidPriorities(promoCategoryImages)
                ? sortByPriority(promoCategoryImages)
                : promoCategoryImages;
              setCategoryImages(categoryImagesArray);
              setPromoImages(promoImagesArray);

              setAllImages(updatedpromoCategoryImages);
            }
          }
        } else {
          //this logic handles only ngo adding event
          const userDocuments = await getNGODocumentsList(user?.ngo_id);

          const promoDocs = userDocuments?.filter((doc) =>
            ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name)
          );

          setPromoImages(promoDocs);

          setInitialValues({
            name: '',
            category_id: null,
            description: '',
            no_of_volunteers: null,
            event_date: dayjs(),
            event_start_time: dayjs(),
            event_end_time: dayjs(),
            meeting_link: '',
            address: '',
            amount_per_person: null
          });
        }
      } catch (error) {
        toast.error('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [eventId, categories.length]);

  useEffect(() => {
    const fetchNgo = async () => {
      if (user?.ngo_id) {
        const ngo = await getNgoById(user.ngo_id);
        setSelectedNgo(ngo);
      }
    };

    fetchNgo();
  }, [user?.ngo_id]);

  useEffect(() => {
    const fetchPromoImages = async () => {
      if (!selectedNgo?.id) return;
      const userDocuments = await getNGODocumentsList(selectedNgo?.id);

      const promoDocs = userDocuments?.filter((doc) =>
        ['Promo Image 1', 'Promo Image 2', 'Promo Image 3'].includes(doc.documentInfo?.name)
      );

      setPromoImages(promoDocs);
    };

    fetchPromoImages();
  }, [selectedNgo?.id]);

  useEffect(() => {
    const fetchNgoDataAndCalculate = async () => {
      if (user?.ngo_id) {
        try {
          const ngoRecords = await getNgoById(user?.ngo_id);
          setNgoInfo(ngoRecords);
          const profilepercentage = await calculateProfileCompletion(ngoRecords);
          setProfileCompletePercentage(profilepercentage);
        } catch (error) {
          console.error('Error fetching NGO data:', error);
        }
      } else {
        setProfileCompletePercentage(100);
      }
    };

    fetchNgoDataAndCalculate();
  }, [user?.ngo_id]);

  const validationSchema = Yup.object({
    name: Yup.string().required('Event name is required'),
    category_id: Yup.string().required('Category is required'),
    description: Yup.string().required('Description is required'),
    no_of_volunteers: Yup.number().required('This field is required').min(1, 'Minimum 1 required'),
    meeting_link: Yup.string().when('eventFormat', {
      is: 'virtual',
      then: Yup.string().required('Meeting link is required')
    }),
    address: Yup.string().when('eventFormat', {
      is: 'physical',
      then: Yup.string().required('Address is required')
    })
  });

  const fetchNgos = useCallback(async (query) => {
    if (query.length < 4) {
      toast.warn('Please enter at least 4 characters to search.');
      return;
    }
    setNgoLoading(true);
    try {
      const response = await getNGOSBySearchTerm(query, 'Verified');
      setNgoList(response);
    } catch (error) {
      console.error('Error fetching NGOs:', error);
    } finally {
      setNgoLoading(false);
    }
  }, []);

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setIsSubmitting(true);

    const payload = {
      ...values,
      campaign_start_date: values.event_date,
      campaign_end_date: values.event_date,
      fullday_event: fullDay,
      event_type: eventFormat,
      volunteer_type: volunteerType,
      ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
      event_start_time: dayjs(values.event_start_time).format('HH:mm:ss'),
      event_end_time: dayjs(values.event_end_time).format('HH:mm:ss'),
      current_address: values.current_address,
      ratio: ratio,
      fileName:
        allImages.length > 0
          ? allImages[0].type === 'categories'
            ? `fetchCategoryImages/${allImages[0].fileName}`
            : `fetchNGODocumentsPDF/${allImages[0].fileName}`
          : promoImages.length > 0
            ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
            : ''
    };
    try {
      if (eventId) {
        if (!values.ngo_id) {
          toast.error('Please select a ngo');
          return;
        }
        await updateCampaign(eventId, {
          ...payload,
          ratio: ratio ? ratio : values.ratio,
          fileName:
            allImages.length > 0
              ? allImages[0].type === 'categories'
                ? `fetchCategoryImages/${allImages[0].fileName}`
                : `fetchNGODocumentsPDF/${allImages[0].fileName}`
              : promoImages.length > 0
                ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
                : '',
          ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id
          // ...addressInformation
        });
        const allImagesArray = [...categoryImages, ...promoImages];

        await updateCampaignPromoImageService(eventId, allImagesArray);
        await patchCampaign(eventId, { imagesUpdated: 'yes' });
        toast.success('Event updated successfully!');
      } else {
        if (eventFormat === 'physical' && (!addressInformation.latitude || !addressInformation.longitude)) {
          toast.error('Please pin your address on the map, locate it, and confirm before submitting.');
          return;
        }
        await addCampaign({
          ...payload,
          ratio: ratio ? ratio : values.ratio,
          status: 'Active',
          ngo_id: user?.ngo_id ? user?.ngo_id : values?.ngo_id,
          ...addressInformation,
          fileName:
            allImages.length > 0
              ? allImages[0].type === 'categories'
                ? `fetchCategoryImages/${allImages[0].fileName}`
                : `fetchNGODocumentsPDF/${allImages[0].fileName}`
              : promoImages.length > 0
                ? `fetchNGODocumentsPDF/${promoImages[0].fileName}`
                : ''
        });
        toast.success('Event added successfully!');
      }
      navigate('/masters/events');
    } catch (error) {
      toast.error('Failed to save event');
    } finally {
      setSubmitting(false);
      setIsSubmitting(false);
    }
  };

  if (loading || !initialValues) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  const toggleImageDeletion = (id, type) => {
    const updateImages = (images) =>
      images.map((img) => (img.id === id ? { ...img, markedForDeletion: img.markedForDeletion === 'yes' ? 'no' : 'yes' } : img));

    if (type === 'promo') {
      setPromoImages((prev) => updateImages(prev));
    } else {
      setCategoryImages((prev) => updateImages(prev));
    }
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const reordered = Array.from(allImages);
    const [removed] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, removed);

    // Assign new priority based on new order
    const reorderedWithPriority = reordered.map((img, index) => ({
      ...img,
      priority: index + 1 // assuming priority starts at 1
    }));

    const newPromo = reorderedWithPriority.filter((img) => img?.documentId);
    const newCat = reorderedWithPriority.filter((img) => !img?.documentId);

    setAllImages(reorderedWithPriority);
    setPromoImages(newPromo);
    setCategoryImages(newCat);
  };

  return (
    <Grid container>
      <Grid item xs={12} sm={7}>
        {user?.ngo_id && selectedNgo && profileCompletePercentage < 100 && (
          <Grid item xs={12} sx={{ marginBottom: 2 }}>
            <ProfileCard ngoInfo={selectedNgo} profileCompletePercentage={profileCompletePercentage} />
          </Grid>
        )}

        <MainCard
          title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}> {eventId ? 'Edit Event' : 'Add Event'}</Typography>}
          subheader={user?.ngo_id ? `By ${selectedNgo?.name}` : ''}
        >
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit} enableReinitialize>
              {({ values, errors, touched, handleChange, handleSubmit, setFieldValue }) => {
                useEffect(() => {
                  const fetchNgo = async () => {
                    if (values.ngo_id && !ngoList.find((ngo) => ngo.id === values.ngo_id)) {
                      try {
                        const ngo = await getNgoById(values.ngo_id);
                        setSelectedNgo(ngo);
                      } catch (error) {
                        console.error(error);
                      }
                    }
                  };

                  fetchNgo();
                }, [values, ngoList, initialValues.ngo_id, user]);

                useEffect(() => {
                  if (values.pincode && values.pincode.length === 6) {
                    fetchLocationDetailsFromPincode(values.pincode, setFieldValue);
                  }
                }, [values.pincode]);

                return (
                  <form onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={12}>
                        <Grid container spacing={3} direction="column">
                          {!user?.ngo_id && (
                            <Grid item xs={12} sm={6}>
                              <Autocomplete
                                options={ngoList}
                                getOptionLabel={(option) => option.name}
                                // value={ngoList.find((cat) => cat.id === values.ngo_id) || null}

                                value={ngoList.find((ngo) => ngo.id === values.ngo_id) || selectedNgo || null}
                                onInputChange={(event, newInputValue) => {
                                  setSearchTerm(newInputValue);
                                }}
                                onChange={(event, newValue) => {
                                  setSelectedNgo(newValue);
                                  setFieldValue('ngo_id', newValue?.id || null);
                                }}
                                loading={ngoLoading}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label="Search NGO"
                                    error={touched.ngo_id && Boolean(errors.ngo_id)}
                                    helperText={touched.ngo_id && errors.ngo_id}
                                    InputProps={{
                                      ...params.InputProps,
                                      endAdornment: (
                                        <>
                                          <InputAdornment position="end">
                                            {ngoLoading ? (
                                              <CircularProgress size={20} />
                                            ) : (
                                              <Button
                                                variant="contained"
                                                size="small"
                                                sx={{ minWidth: '60px', px: 2 }}
                                                onClick={() => fetchNgos(searchTerm)}
                                              >
                                                Search
                                              </Button>
                                            )}
                                          </InputAdornment>
                                          {params.InputProps.endAdornment}
                                        </>
                                      )
                                    }}
                                  />
                                )}
                              />
                            </Grid>
                          )}

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Event Name"
                              name="name"
                              required
                              value={values.name}
                              onChange={handleChange}
                              error={touched.name && !!errors.name}
                              helperText={touched.name && errors.name}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Autocomplete
                              fullWidth
                              options={categories}
                              getOptionLabel={(option) => option.name || ''}
                              value={categories.find((cat) => cat.id === values.category_id) || null}
                              onChange={(event, newValue) => {
                                const selectedId = newValue ? newValue.id : '';
                                setSelectedCategoryId(selectedId);
                                setFieldValue('category_id', selectedId);

                                if (newValue) {
                                  setRatio(newValue.ratio || '');
                                  setFieldValue('ratio', newValue.ratio || '');
                                  setCategoryImages(newValue.images || []);
                                }
                              }}
                              isOptionEqualToValue={(option, value) => option.id === value.id}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Category"
                                  name="category_id"
                                  required
                                  error={touched.category_id && Boolean(errors.category_id)}
                                  helperText={touched.category_id && errors.category_id}
                                />
                              )}
                            />
                          </Grid>

                          {!user?.ngo_id && (
                            <Grid item xs={12} sm={6}>
                              <TextField
                                label="Ratio"
                                type="text"
                                inputProps={{
                                  inputMode: 'decimal',
                                  pattern: '[0-9]*[.]?[0-9]*',
                                  maxLength: 4
                                }}
                                value={values.ratio || ''}
                                onChange={(e) => {
                                  const newValue = e.target.value;
                                  setRatio(newValue); // Update local state
                                  setFieldValue('ratio', newValue); // Update formik values
                                }}
                                fullWidth
                              />
                            </Grid>
                          )}

                          <Grid item xs={12} sm={6}>
                            {' '}
                            <TextField
                              label="Description"
                              name="description"
                              multiline
                              rows={4}
                              fullWidth
                              required
                              value={values.description}
                              onChange={handleChange}
                              inputProps={{ maxLength: MAX_LENGTH }}
                              error={touched.description && !!errors.description}
                              helperText={touched.description && errors.description}
                            />
                            <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'gray', mt: 0.5 }}>
                              {values?.description?.length ? MAX_LENGTH - values?.description?.length : MAX_LENGTH} characters left
                            </Typography>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            {' '}
                          </Grid>

                          {/* <Grid container spacing={2} sx={{ mb: 1, mt: 1 }}> */}
                          <Grid item xs={12} sm={6}>
                            <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                              <Grid item xs={12} sm={6}>
                                <Typography variant="subtitle1" gutterBottom>
                                  Event Format *
                                </Typography>
                                <RadioGroup row value={eventFormat} onChange={(e) => setEventFormat(e.target.value)}>
                                  <FormControlLabel value="physical" control={<Radio color="primary" />} label="Physical" />
                                  <FormControlLabel value="virtual" control={<Radio color="primary" />} label="Virtual" />
                                </RadioGroup>
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <Typography variant="subtitle1" gutterBottom>
                                  Volunteer Type *
                                </Typography>
                                <RadioGroup row value={volunteerType} onChange={(e) => setVolunteerType(e.target.value)}>
                                  <FormControlLabel value="self" control={<Radio color="primary" />} label="Self (Free)" />
                                  <FormControlLabel value="paid" control={<Radio color="primary" />} label="Paid" />
                                </RadioGroup>
                              </Grid>
                            </Grid>
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                              <Grid item xs={12} sm={6}>
                                <MobileDatePicker
                                  label="Event Date *"
                                  minDate={dayjs()}
                                  maxDate={dayjs().add(102, 'day')}
                                  value={values.event_date}
                                  onChange={(value) => setFieldValue('event_date', value)}
                                  slotProps={{ textField: { fullWidth: true } }}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  type="text"
                                  name="no_of_volunteers"
                                  label="Number of Volunteers Required"
                                  required
                                  value={values.no_of_volunteers}
                                  onChange={(e) => {
                                    const val = e.target.value;
                                    if (/^\d*$/.test(val)) {
                                      setFieldValue('no_of_volunteers', val);
                                    }
                                  }}
                                  error={touched.no_of_volunteers && !!errors.no_of_volunteers}
                                  helperText={touched.no_of_volunteers && errors.no_of_volunteers}
                                  fullWidth
                                />
                              </Grid>
                            </Grid>
                          </Grid>

                          {volunteerType == 'paid' && (
                            <Grid item xs={12} sm={6}>
                              <TextField
                                name="amount_per_person"
                                label="Amount per Person (₹)"
                                required
                                value={volunteerType === 'self' ? null : values.amount_per_person}
                                onChange={(e) => {
                                  const val = e.target.value;
                                  if (/^\d*$/.test(val) && Number(val) >= 1) {
                                    setFieldValue('amount_per_person', val);
                                  }
                                }}
                                InputProps={{ inputProps: { min: 1 } }}
                                disabled={volunteerType === 'self'}
                                error={touched.amount_per_person && !!errors.amount_per_person}
                                helperText={
                                  volunteerType === 'self' ? 'Free volunteer (₹0)' : touched.amount_per_person && errors.amount_per_person
                                }
                                fullWidth
                              />
                            </Grid>
                          )}

                          <Grid item xs={12}>
                            <FormControlLabel
                              control={
                                <Checkbox checked={fullDay === 'yes'} onChange={(e) => setFullDay(e.target.checked ? 'yes' : 'no')} />
                              }
                              label="Full Day Event *"
                            />
                          </Grid>

                          {fullDay == 'no' && (
                            <Grid item xs={12} sm={6}>
                              <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                {/* </Grid> */}

                                <Grid item xs={12} sm={6}>
                                  <MobileTimePicker
                                    label="Start Time *"
                                    value={values.event_start_time}
                                    onChange={(value) => setFieldValue('event_start_time', value)}
                                    slotProps={{ textField: { fullWidth: true } }}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <MobileTimePicker
                                    label="End Time *"
                                    value={values.event_end_time}
                                    onChange={(value) => setFieldValue('event_end_time', value)}
                                    slotProps={{ textField: { fullWidth: true } }}
                                  />
                                </Grid>
                              </Grid>
                            </Grid>
                          )}

                          {eventFormat === 'virtual' ? (
                            <Grid item xs={12} sm={6}>
                              {' '}
                              <TextField
                                label="Meeting Link"
                                name="meeting_link"
                                value={values.meeting_link}
                                onChange={handleChange}
                                fullWidth
                                required
                                error={touched.meeting_link && !!errors.meeting_link}
                                helperText={touched.meeting_link && errors.meeting_link}
                              />
                            </Grid>
                          ) : (
                            <>
                              <Grid item xs={12} sm={6}>
                                <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      required
                                      label="Pincode"
                                      name="pincode"
                                      value={values?.pincode || ''}
                                      // disabled={values.pincode}
                                      onChange={handleChange}
                                    />
                                  </Grid>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      required
                                      label="City"
                                      name="place_name"
                                      value={values.place_name || ''}
                                      // disabled={values.place_name}
                                      disabled
                                      onChange={handleChange}
                                    />
                                  </Grid>
                                </Grid>
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <Grid container md={12} rowSpacing={2} columnSpacing={1}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      required
                                      // disabled={values.state}
                                      label="State"
                                      name="state"
                                      value={values.state || ''}
                                      disabled
                                      onChange={handleChange}
                                    />
                                  </Grid>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      fullWidth
                                      required
                                      label="Country"
                                      name="country"
                                      value={values.country || ''}
                                      disabled
                                      // disabled={values.country}
                                      onChange={handleChange}
                                    />
                                  </Grid>
                                </Grid>
                              </Grid>

                              <Grid item xs={12} sm={6}>
                                <TextField
                                  fullWidth
                                  multiline
                                  rows={4}
                                  label="Address"
                                  required
                                  name="current_address"
                                  value={values?.current_address}
                                  onChange={(e) => {
                                    handleChange(e); // pass the event to Formik
                                    setAddressInformation((prev) => ({
                                      ...prev,
                                      current_address: e.target.value // safer than values?.current_address
                                    }));
                                  }}
                                  InputLabelProps={{ shrink: true }}
                                  // inputProps={{ maxLength: 300 }}
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <Button
                                          variant="contained"
                                          color="primary"
                                          onClick={() => handleSearchSubmit(values?.current_address)}
                                          disabled={isLoadingLocate}
                                        >
                                          {isLoadingLocate ? <CircularProgress size={20} color="inherit" /> : 'Pin Your Address'}
                                        </Button>
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </Grid>
                            </>
                          )}
                          {(categoryImages?.length > 0 || promoImages?.length > 0) && eventId && (
                            <Grid item xs={12}>
                              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                                Promo Images
                              </Typography>
                              <DragDropContext onDragEnd={handleDragEnd}>
                                <Droppable droppableId="images" direction="horizontal">
                                  {(provided) => (
                                    <Box
                                      ref={provided.innerRef}
                                      {...provided.droppableProps}
                                      sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}
                                    >
                                      {allImages.map((img, index) => (
                                        <Draggable draggableId={`${img.type}-${img.id}`} index={index} key={`${img.type}-${img.id}`}>
                                          {(provided) => (
                                            <Box
                                              ref={provided.innerRef}
                                              {...provided.draggableProps}
                                              {...provided.dragHandleProps}
                                              sx={{
                                                position: 'relative',
                                                boxShadow: 7,
                                                borderRadius: 2,
                                                overflow: 'hidden',
                                                width: 100,
                                                height: 80
                                              }}
                                            >
                                              <Tooltip title={img.markedForDeletion === 'yes' ? 'Add Image' : 'Remove Image'}>
                                                <IconButton
                                                  size="large"
                                                  onClick={() => toggleImageDeletion(img.id, img.type)}
                                                  sx={{
                                                    position: 'absolute',
                                                    top: -12,
                                                    right: -12,
                                                    color: img.markedForDeletion === 'yes' ? 'green' : 'red',
                                                    zIndex: 3,
                                                    '&:hover': {
                                                      backgroundColor: 'white',
                                                      color: img.markedForDeletion === 'yes' ? 'darkgreen' : 'darkred',
                                                      borderRadius: 10
                                                    }
                                                  }}
                                                >
                                                  {img.markedForDeletion === 'yes' ? <PlusCircleFilled /> : <MinusCircleFilled />}
                                                </IconButton>
                                              </Tooltip>
                                              <CardMedia
                                                component="img"
                                                image={`${BASE_URL}/${img.documentId || img.type == 'promo' ? 'fetchNGODocumentsPDF' : 'fetchCategoryImages'}/${img.fileName}`}
                                                alt={img.description || 'Image'}
                                                sx={{
                                                  width: '100%',
                                                  height: '100%',
                                                  objectFit: 'cover',
                                                  opacity: img.markedForDeletion === 'yes' ? 0.4 : 1,
                                                  filter: img.markedForDeletion === 'yes' ? 'grayscale(100%)' : 'none',
                                                  transition: '0.3s ease'
                                                }}
                                              />
                                            </Box>
                                          )}
                                        </Draggable>
                                      ))}
                                      {provided.placeholder}
                                    </Box>
                                  )}
                                </Droppable>
                              </DragDropContext>
                            </Grid>
                          )}

                          <Grid item xs={12} sx={{ mt: 2 }}>
                            <Button variant="contained" type="submit" disabled={user?.ngo_id && profileCompletePercentage < 100}>
                              {isSubmitting ? (
                                eventId ? (
                                  <CircularProgress size={24} color="secondary" />
                                ) : (
                                  <CircularProgress size={24} color="secondary" />
                                )
                              ) : eventId ? (
                                'Update Event'
                              ) : (
                                'Add Event'
                              )}
                            </Button>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </form>
                );
              }}
            </Formik>
          </LocalizationProvider>

          <AddressDialog
            isOpen={isDialogOpen}
            onClose={() => setIsDialogOpen(false)}
            isLoaded={isLoaded}
            markerPosition={markerPosition}
            onMarkerDragEnd={handleMarkerDragEnd}
            addressInformation={addressInformation}
            campaignData={campaignData}
          />
          <ToastContainer />
        </MainCard>
      </Grid>
    </Grid>
  );
};

export default EventForm;
