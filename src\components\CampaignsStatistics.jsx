import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  CircularProgress,
  Button,
  Chip,
  LinearProgress,
  Tooltip,
  Avatar,
  Select,
  MenuItem,
  FormControl
} from '@mui/material';
import { <PERSON><PERSON><PERSON> } from '@mui/x-charts/PieChart';
import { LineChart } from '@mui/x-charts/LineChart';

import { FundOutlined, HeartOutlined, TrophyOutlined, RiseOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';

// Services
import { getNgoComprehensiveStats } from 'pages/masters/apis/campaign-event-stats.service';

// Components
import CustomerTableWithoutFilter from 'sections/apps/customer/CustomerTableWithoutFilter';
import { formatAmountIndian } from 'utils/permissionUtils';
import useAuth from 'hooks/useAuth';

const COLORS = ['#ffae5f', '#ff9800', '#f57c00', '#e65100', '#bf360c', '#8d6e63'];

const CampaignsEventsStatistics = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timePeriod, setTimePeriod] = useState('3M');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Detail view states
  const [showDetailView, setShowDetailView] = useState(false);
  const [detailViewType, setDetailViewType] = useState(''); // 'donations', 'volunteers', 'rsvps'
  const [detailViewData, setDetailViewData] = useState([]);
  const [detailViewTitle, setDetailViewTitle] = useState('');
  const [selectedDonor, setSelectedDonor] = useState(null);

  const ngoId = user?.ngo_id || null;

  useEffect(() => {
    fetchNgoStats();
  }, [ngoId]);

  const fetchNgoStats = async () => {
    try {
      setLoading(true);
      const response = await getNgoComprehensiveStats(ngoId);

      if (response?.success) {
        setStats(response.data);
      } else {
        toast.error('Failed to fetch campaign statistics');
      }
    } catch (error) {
      console.error('Error fetching NGO stats:', error);
      toast.error('An error occurred while fetching campaign statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (campaignId) => {
    navigate(`/dashboard/campaign-report/${campaignId}`);
  };

  // Handle clicking on donor count to show donation details
  const handleDonorClick = (donor) => {
    if (donor.donationDetails && donor.donationDetails.length > 0) {
      setSelectedDonor(donor);
      setDetailViewData(donor.donationDetails);
      setDetailViewType('donations');
      setDetailViewTitle(`${donor.donorName}'s Donation History`);
      setShowDetailView(true);
    }
  };

  // Handle back button from detail view
  const handleBackToMain = () => {
    setShowDetailView(false);
    setDetailViewType('');
    setDetailViewData([]);
    setDetailViewTitle('');
    setSelectedDonor(null);
  };

  // Get columns for donation details table
  const getDonationDetailsColumns = () => [
    {
      accessorKey: 'campaignName',
      header: 'Campaign',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600}>
          {cell.row.original.campaignName || 'N/A'}
        </Typography>
      )
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ cell }) => (
        <Typography variant="body2" fontWeight={600} color="success.main">
          {formatCurrency(cell.row.original.amount)}
        </Typography>
      )
    },
    {
      accessorKey: 'donationDate',
      header: 'Date',
      cell: ({ cell }) => <Typography variant="body2">{dayjs(cell.row.original.donationDate).format('DD/MM/YYYY HH:mm')}</Typography>
    },
    {
      accessorKey: 'impactCreated',
      header: 'Impact',
      cell: ({ cell }) => <Chip label={`${cell.row.original.impactCreated || 0} lives`} size="small" color="primary" variant="outlined" />
    },
    {
      accessorKey: 'paymentId',
      header: 'Payment ID',
      cell: ({ cell }) => (
        <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
          {cell.row.original.paymentId || 'N/A'}
        </Typography>
      )
    }
  ];

  const formatCurrency = (amount) => {
    if (!amount) return '₹0';
    return '₹' + amount.toLocaleString('en-IN');
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'completed':
        return 'primary';
      case 'draft':
        return 'warning';
      case 'paused':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading campaign statistics...</Typography>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary">
          No campaign statistics available
        </Typography>
      </Box>
    );
  }

  // Show detail view if active
  if (showDetailView) {
    return (
      <Box sx={{ p: 3 }}>
        {/* Back Button */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            onClick={handleBackToMain}
            sx={{
              borderColor: '#ffae5f',
              color: '#ffae5f',
              '&:hover': {
                borderColor: '#ff9800',
                backgroundColor: 'rgba(255, 174, 95, 0.1)'
              }
            }}
          >
            ← Back to Dashboard
          </Button>
        </Box>

        {/* Detail View Header */}
        <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
          {detailViewTitle}
        </Typography>

        {/* Selected Donor Summary */}
        {selectedDonor && (
          <Card sx={{ mb: 4, border: '1px solid rgba(255, 174, 95, 0.2)', borderRadius: 3 }}>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {formatCurrency(selectedDonor.totalDonated)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Donated
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                      {selectedDonor.donationCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Donations
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {selectedDonor.email}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Email Address
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {dayjs(selectedDonor.lastDonationDate).format('DD/MM/YYYY')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last Donation
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Detail Data Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Donation Details ({detailViewData.length} transactions)
            </Typography>
            <CustomerTableWithoutFilter data={detailViewData} columns={getDonationDetailsColumns()} category="Donation Details" />
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
        Campaign Statistics Dashboard
      </Typography>

      {/* KPI Cards */}
      {/* <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <FundOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {formatCurrency(stats.totalFundGoal)}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Fund Goal
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Across all campaigns
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <HeartOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {stats.livesImpacted?.toLocaleString() || 0}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Lives Impacted
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                People helped through campaigns
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <TrophyOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {formatCurrency(stats.fundCollected?.amount)}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Fund Collected
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {stats.fundCollected?.percentage?.toFixed(1)}% of goal achieved
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <RiseOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f' }}>
                  {stats.totalCampaigns || 0}
                </Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                Total Campaigns
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {stats.activeCampaigns || 0} currently active
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid> */}

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Fund Goal */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <FundOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Fund Goal
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Across all campaigns
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {formatCurrency(stats.totalFundGoal)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Lives Impacted */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <HeartOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Lives Impacted
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    People helped through campaigns
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {stats.livesImpacted?.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Fund Collected */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <TrophyOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Fund Collected
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {stats.fundCollected?.percentage?.toFixed(1)}% of goal achieved
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {formatCurrency(stats.fundCollected?.amount)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Campaigns */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              border: '1px solid rgba(255, 174, 95, 0.2)',
              borderRadius: 3,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                borderColor: '#ffae5f',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff4ed 100%)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: 'rgba(255, 174, 95, 0.15)',
                    width: 48,
                    height: 48,
                    mr: 2,
                    boxShadow: '0 2px 8px rgba(255, 174, 95, 0.2)'
                  }}
                >
                  <RiseOutlined style={{ color: '#ffae5f', fontSize: 22 }} />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.3 }}>
                    Total Campaigns
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {stats.activeCampaigns || 0} currently active
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: '#ffae5f', textAlign: 'center' }}>
                {stats.totalCampaigns || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Lives Impacted Breakdown - Pie Chart */}
        {/* <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Lives Impacted by Category
              </Typography>
              {stats.livesImpactedBreakdown && stats.livesImpactedBreakdown.length > 0 ? (
                <PieChart
                  series={[
                    {
                      data: stats.livesImpactedBreakdown.map((item, index) => ({
                        id: index,
                        value: item.livesImpacted,
                        label: item.categoryName,
                        color: COLORS[index % COLORS.length]
                      }))
                    }
                  ]}
                  width={400}
                  height={300}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No category data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid> */}

        {/* Fund Collection Trend - Line Chart */}
        <Grid item xs={12} md={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Fund Collection Trend</Typography>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <Select
                    value={timePeriod}
                    onChange={(e) => {
                      console.log('Selected time period:', e.target.value);

                      setTimePeriod(e.target.value);
                    }}
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(255, 174, 95, 0.3)'
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#ffae5f'
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#ffae5f'
                      }
                    }}
                  >
                    <MenuItem value="3M">3 Months</MenuItem>
                    <MenuItem value="6M">6 Months</MenuItem>
                    <MenuItem value="1Y">1 Year</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              {stats.fundCollectionTrend && stats.fundCollectionTrend[timePeriod] && stats.fundCollectionTrend[timePeriod].length > 0 ? (
                <>
                  <LineChart
                    xAxis={[
                      {
                        scaleType: 'point',
                        data: stats.fundCollectionTrend[timePeriod].map((item) => item.month)
                      }
                    ]}
                    yAxis={[
                      {
                        valueFormatter: (value) => formatAmountIndian(value)
                      }
                    ]}
                    series={[
                      {
                        data: stats.fundCollectionTrend[timePeriod].map((item) => item.amount),
                        label: 'Amount Collected',
                        color: '#ffae5f'
                      }
                    ]}
                    width={600}
                    height={300}
                    sx={{ ml: 10 }}
                    grid={{ vertical: true, horizontal: true }}
                  />

                  <Box sx={{ mt: 2, ml: 10 }}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Legend:</strong> K = Thousand, L = Lakh, Cr = Crore
                    </Typography>
                  </Box>
                </>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No trend data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Donors Table */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Donors
              </Typography>
              {stats.topDonors && stats.topDonors.length > 0 ? (
                <CustomerTableWithoutFilter
                  data={stats.topDonors}
                  columns={[
                    {
                      accessorKey: 'donorName',
                      header: 'Donor Name',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600}>
                          {cell.row.original.donorName}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'email',
                      header: 'Email',
                      cell: ({ cell }) => (
                        <Typography variant="body2" color="text.secondary">
                          {cell.row.original.email}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'totalDonated',
                      header: 'Total Donated',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600} color="success.main">
                          {formatCurrency(cell.row.original.totalDonated)}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'donationCount',
                      header: 'Donations',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.donationCount}
                          size="small"
                          color="primary"
                          variant="outlined"
                          onClick={() => handleDonorClick(cell.row.original)}
                          sx={{
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 174, 95, 0.1)',
                              borderColor: '#ffae5f'
                            }
                          }}
                        />
                      )
                    },
                    {
                      accessorKey: 'lastDonationDate',
                      header: 'Last Donation',
                      cell: ({ cell }) => (
                        <Typography variant="body2">{dayjs(cell.row.original.lastDonationDate).format('DD/MM/YYYY')}</Typography>
                      )
                    }
                  ]}
                  category="Top Donors"
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No donor data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Campaign Performance Table */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Campaign Performance
              </Typography>
              {stats.campaignList && stats.campaignList.length > 0 ? (
                <CustomerTableWithoutFilter
                  data={stats.campaignList}
                  columns={[
                    {
                      accessorKey: 'name',
                      header: 'Campaign Name',
                      cell: ({ cell }) => (
                        <Box>
                          <Tooltip title={cell.row.original.name} arrow placement="top-start">
                            <Typography
                              variant="body2"
                              fontWeight={600}
                              onClick={() => handleViewReport(cell.row.original.id)}
                              sx={{
                                cursor: 'pointer',
                                color: '#ffae5f',
                                fontWeight: 700,
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                '&:hover': {
                                  textDecoration: 'underline'
                                }
                              }}
                            >
                              {cell.row.original.name}
                            </Typography>
                          </Tooltip>
                          <Tooltip title={cell.row.original.category} arrow placement="bottom-end">
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}
                            >
                              {cell.row.original.category}
                            </Typography>
                          </Tooltip>
                        </Box>
                      )
                    },
                    {
                      accessorKey: 'status',
                      header: 'Status',
                      cell: ({ cell }) => (
                        <Chip
                          label={cell.row.original.status}
                          size="small"
                          color={getStatusColor(cell.row.original.status)}
                          variant="filled"
                        />
                      )
                    },
                    {
                      accessorKey: 'fundGoal',
                      header: 'Goal',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600}>
                          {formatCurrency(cell.row.original.fundGoal)}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'fundReceived',
                      header: 'Raised',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600} color="success.main">
                          {formatCurrency(cell.row.original.fundReceived)}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'percentageAchieved',
                      header: 'Progress',
                      cell: ({ cell }) => {
                        const percentage = cell.row.original.percentageAchieved || 0;
                        return (
                          <Box sx={{ width: '100%' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Typography variant="body2" sx={{ minWidth: 35 }}>
                                {percentage.toFixed(1)}%
                              </Typography>
                            </Box>
                            <LinearProgress variant="determinate" value={Math.min(percentage, 100)} sx={{ height: 6, borderRadius: 3 }} />
                          </Box>
                        );
                      }
                    },
                    {
                      accessorKey: 'livesImpacted',
                      header: 'Lives Impacted',
                      cell: ({ cell }) => (
                        <Typography variant="body2" fontWeight={600} color="primary.main">
                          {cell.row.original.livesImpacted?.toLocaleString() || 0}
                        </Typography>
                      )
                    },
                    {
                      accessorKey: 'campaignEndDate',
                      header: 'End Date',
                      cell: ({ cell }) => (
                        <Typography variant="body2">{dayjs(cell.row.original.campaignEndDate).format('DD/MM/YYYY')}</Typography>
                      )
                    }
                  ]}
                  category="Campaign Performance"
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No campaign data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CampaignsEventsStatistics;
